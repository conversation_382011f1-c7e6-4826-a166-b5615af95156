import {computed, ComputedRef, ref, Ref, watch} from 'vue';
import EnrollHousehold from 'components/enrollments/forms/EnrollHousehold.vue';
import ChooseCoverages from 'components/enrollments/forms/ChooseCoverages.vue';
import EnrollmentCoverageTable from 'components/enrollments/cards/EnrollmentCoverageTable.vue';
import ChooseBenefits from 'components/enrollments/forms/ChooseBenefits.vue';
import EnrollmentCafeTable from 'components/enrollments/cards/EnrollmentCafeTable.vue';
import HouseholdTax from 'components/households/cards/HouseholdTax.vue';
import IncomeAnalysis from 'components/enrollments/cards/IncomeAnalysis.vue';
import DocsDisplay from 'components/plans/docs/cards/DocsDisplay.vue';
import DocsList from 'components/plans/docs/cards/DocsList.vue';
import HhEnrollTable from 'components/enrollments/forms/HhEnrollTable.vue';

import {Enrollment, getFlags, priorities} from 'components/enrollments/utils/flags';
import {cafeKeys, Plan} from 'components/plans/utils';
import {HFind} from 'src/utils/hFind';
import {useCoverages} from 'stores/coverages';
import {useCams} from 'stores/cams';
import {incomeDisplay} from 'components/comps/utils';
import {getCoverageRate} from 'components/coverages/utils/display';
import {getElectedContributions, getEmployerFlexCredits} from 'components/plans/utils/calcs';
import {dollarString} from 'src/utils/global-methods';
import {AnyRef} from 'src/utils/types';
import {convertCmsPerson} from 'components/market/household/utils/index';
import {useMarketplace} from 'stores/marketplace';
import {useCmsStore} from 'stores/cms-store';
import {getPerson} from 'components/enrollments/ichra/utils/cms-index';


type Config = {
    enrollment: Ref<Enrollment> | ComputedRef<Enrollment>,
    plan: Ref<Plan> | ComputedRef<Plan>,
    person: Ref<any> | ComputedRef<any>,
    shop?:AnyRef<any>
}

export const enrollmentContributions = ({enrollment, plan, person, shop}: Config) => {
    const cStore:any = useCoverages();
    const camsStore = useCams();
    const marketStore = useMarketplace();
    const cmsStore = useCmsStore();

    const {h$: camsData} = HFind({
        store: camsStore as any,
        pause: computed(() => !plan.value || !person.value),
        params: computed(() => {
            return {
                query: {
                    person: person.value?._id
                },
                runJoin: {'cams_groups': plan.value?.groups}
            }
        })
    })

    const cams = computed(() => camsData.data || [])

    const incomeSources = incomeDisplay(cams);

    const coverages = computed(() => c$.data);


    const coverageIds = computed(() => {
        const arr:any = [];
        const ids = Object.keys(enrollment.value?.coverages || {});
        for(let i = 0; i < ids.length; i++){
            arr.push(ids[i])
            if(enrollment.value.coverages[ids[i]].individual_coverage) arr.push(enrollment.value.coverages[ids[i]].individual_coverage);

        }
        return arr;
    })
    const {h$: c$} = HFind({
        store: cStore as any,
        limit: computed(() => coverageIds.value.length),
        params: computed(() => {
            return {
                query: {
                    _id: {$in: coverageIds.value }
                }
            }
        })
    })

    /** THIS DOES NOT GO ON SERVER SIDE CALCS. This is not a critical issue as no liability is incurred by the group unless the employee elects a 125 contribution. So we measure it here to prompt them to do that */
    const cmsPlanIds = computed(() => {
        const arr:Array<string> = [];
        const ids = Object.keys(enrollment.value?.coverages || {});

        for(let i = 0; i < ids.length; i++){
            const policyId = enrollment.value.coverages[ids[i]].policy
            if(policyId) arr.push(policyId);
        }
        return arr;
    })
    const cmsPoliciesById = ref({})
    const loadCmsPolicies = async (tries = 0) => {
        if(!cmsPlanIds.value.length) {
            if(tries < 10) return setTimeout(() => loadCmsPolicies(tries+1), 500);
            return;
        }
        const data = {
            household: {
                people: Object.keys(enrollment.value.enrolled || {}).map(a => getPerson(a, enrollment.value)),
                income: enrollment.value.householdIncome || 100000,
            },
            place: {
                state: ((enrollment.value.county || {}) as any).stateCode,
                countyfips: ((enrollment.value.county || {}) as any).fips,
                zipcode: ((enrollment.value.address || {}) as any).postal
            }
        }
        if(shop?.value){
            if(shop.value?.stats?.income) data.household.income = shop.value.stats?.income;
            if(shop.value?.stats?.people) data.household.people = [convertCmsPerson(person.value), ...shop?.value?.stats?.people || [].map(a => convertCmsPerson(a))]
        }
        const find_by_id = {
            id: cmsPlanIds.value,
            ...data
        }
        const plans = await marketStore.find({ query: {}, runJoin: { find_by_id }} as any)
            .catch(err => {
                console.log(`Error loading plans for enrollment contributions: ${err.message}`)
                if(tries < 10) setTimeout(() => loadCmsPolicies(tries+1), 500);
            })
        if(plans) {
            cmsStore.setPolicies(plans.data);
            plans.data.forEach(p => {
                cmsPoliciesById.value[p._id] = p;
            })
        }
    }
    watch(cmsPlanIds, (nv, ov) => {
        if(nv?.length && nv.length !== ov?.length){
            loadCmsPolicies();
        }
    }, {immediate: true})

    const neededContributions = computed(() => {
        let total = 0;
        let needTotal = 0;
        let postTax = 0;
        let employerCoverages = 0;
        const byCoverage:any = {};
        // console.log('looping coverages', c$.data);
        for (const c of (c$.data || []).filter(a => a.covered === 'group')) {
            let num = 0
            let config:any = {}
            let isPost = c.postTax;
            if(c.ichra || c.shop){
                /** This because only an ICHRA currently works for pretax individual plan purchases at the end of 2025 */
                if(!c.ichra) isPost = true;
                config = (enrollment.value?.coverages || {})[c._id] || {}
                if(config.premium) {
                    num = config.premium
                    if(config.policy && !c.ichra) num -= config.aptc?.aptc || 0;
                }
                else if(config.policy) {
                    num = cmsPoliciesById.value[config.policy]?.premium || 0 - (config.aptc?.aptc || 0)
                } else if(config.individual_coverage) {
                    num = getCoverageRate({coverage: cStore.getFromStore(config.individual_coverage).value, enrollment: enrollment.value }) || 0
                }
            } else {
                num = getCoverageRate({ coverage: c, enrollment: enrollment.value}) || 0;
            }

            const ec = (plan.value?.coverages || {})[c._id]?.employerContribution || {};
            const eType = enrollment.value?.type || 'single'
            let ecTotal= eType === 'family' ? ec.family || 0 : ec.single || 0
            if(ec.type === 'percent'){
                ecTotal = (ecTotal/100) * num
            }
            // console.log('got ec', ec, num);
            byCoverage[c._id] = { carrierLogo: c.carrierLogo, carrierName: c.carrierName, name: c.name, needed: Number(num || 0)}
            if (ec) {
                employerCoverages += ecTotal
                num -= ecTotal
                byCoverage[c._id].employer = ecTotal;
            }
            byCoverage[c._id].employee = num;
            total += num;
            needTotal += num
            if(!isPost && c.ichra){
                const { type } = config.fullPolicy || config.fullCoverage || {};
                if(type === 'hs') isPost = true;
            }
            if (isPost) postTax += num;
        }

        const needed = {
            total: needTotal,
            postTax,
            preTax: needTotal - postTax
        }

        return {needed, total, postTax, preTax: total - postTax, employerCoverages, byCoverage};
    })

    const electedContributions = computed(() => {
        return getElectedContributions(enrollment.value)
    })

    const employerContribution = computed(() => {
        return getEmployerFlexCredits(plan.value, {
            enrollment: enrollment.value,
            needs: neededContributions.value,
            totalIncome: incomeSources.value?.total,
            baseIncome: incomeSources.value?.amount,
            contributions: electedContributions.value.total
        })
    })

    const contributions = computed(() => {
        const { preTax, postTax, total } = neededContributions.value
        return {
            needed: { preTax, postTax, total },
            elected: electedContributions.value.total,
            employer: { cafe: employerContribution.value, coverage: neededContributions.value.employerCoverages },
            byCoverage: neededContributions.value.byCoverage || {}
        }
    })

    const {acknowledgements} = getFlags({plan, enrollment, coverages, contributions})

    const compliance = computed(() => {
        const obj: any = {};
        const eCafe = enrollment.value?.cafe || {'hsa': {amount: 0}}
        for (const k in plan.value?.cafe || {}) {
            const eObj = eCafe[k] || {};
            const {undone = []} = acknowledgements(k);
            const priority = eObj?.optOut ? 3 : undone[0]?.priority || 3;
            obj[k as string] = {
                display: eObj?.optOut ? 'Opted Out' : dollarString(eObj?.amount || 0, '$', 0),
                label: cafeKeys[k as keyof typeof cafeKeys].name,
                message: priorities[priority as keyof typeof priorities],
                priority
            }
        }
        return obj;
    })
    return {
        contributions,
        c$,
        camsData,
        incomeSources,
        compliance
    }
}

export const enrollmentClosed = (er: any) => {
    const dt = new Date().getTime();
    return new Date(er?.close || dt).getTime() < dt;
}

export const enrollmentSections = ({enrollment, plan, person, shop}: Config) => {

    const {
        contributions,
        c$,
        camsData,
        incomeSources,
        compliance
    } = enrollmentContributions({enrollment, plan, person, shop})

    const isClosed = computed(() => {
        return enrollmentClosed(enrollment.value)
    })

    const isComplete = computed(() => enrollment.value?.status === 'complete')

    const sections = computed(() => {
        return {
            'household': {
                label: 'Household',
                component: EnrollHousehold,
                mini: HhEnrollTable,
                miniAttrs: {
                    person: person.value,
                    enrollment: enrollment.value
                },
                hide: isComplete.value && isClosed.value,
                attrs: {
                    plan: plan.value,
                    modelValue: enrollment.value
                }
            },
            'coverage': {
                label: 'Coverage',
                component: ChooseCoverages,
                mini: EnrollmentCoverageTable,
                miniAttrs: {
                    plan: plan.value,
                    enrollment: enrollment.value,
                    coverages: c$.data
                },
                hide: isComplete.value && isClosed.value,
                attrs: {
                    plan: plan.value,
                    modelValue: enrollment.value
                }
            },
            'benefits': {
                label: 'Pay Elections',
                component: ChooseBenefits,
                mini: EnrollmentCafeTable,
                miniAttrs: {
                    enrollment: enrollment.value,
                    coverages: c$.data,
                    compliance: compliance.value
                },
                hide: isComplete.value && isClosed.value,
                attrs: {
                    plan: plan.value,
                    modelValue: enrollment.value,
                    contributions: contributions.value
                }
            },
            'income': {
                label: 'Income',
                mini: HouseholdTax,
                miniAttrs: {
                    toggle: true,
                    person: person.value,
                    plan: plan.value,
                    enrollment: enrollment.value
                },
                component: IncomeAnalysis,
                attrs: {
                    enrollment: enrollment.value,
                    plan: plan.value,
                    person: person.value
                }
            },
            'docs': {
                label: 'Plan Docs',
                mini: DocsList,
                miniAttrs: {
                    plan: plan.value
                },
                component: DocsDisplay,
                attrs: {
                    plan: plan.value
                }
            }
        }
    })
    return {
        sections,
        contributions,
        c$,
        camsData,
        incomeSources,
        compliance,
        isComplete,
        isClosed
    }
}
