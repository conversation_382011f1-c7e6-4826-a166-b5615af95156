import {getCoverageRate} from 'components/coverages/utils/display';
import {useCoverages} from 'stores/coverages';
;
type Options = { covId:string, enrollment?:any, plan:any}
export const getErContribution = ({ covId, enrollment, plan }:Options) => {
    const config = (plan?.coverages || {})[covId]?.employerContribution;
    if(config){
        const type = enrollment?.type || 'single'
        const amt = config[type] || 0;
        if(config.type === 'percent'){
            const c = (useCoverages() as any).getFromStore(covId);
            let num = 0
            if(c?.value?.shop){
                const myChoice = (enrollment.coverages || {})[covId]
                num = myChoice.premium || (myChoice?.fullPolicy?.premium - (myChoice.aptc || 0)) || myChoice.fullCoverage?.premium || 0;
            } else num = getCoverageRate({ coverage: c.value, enrollment }) || 0;
            return num * (amt / 100);
        } else return amt;

    } else return 0;
}

export const getErContributionSymbols = (covId:string, plan:any) => {
    const config = (plan?.coverages || {})[covId]?.employerContribution;
    if(config){
        const { type } = config;
        if(type === 'percent') return { prefix: '', suffix: '%'};
        else return { prefix: '$', suffix: ''}
    } else return { prefix: '', suffix: '' };
}

