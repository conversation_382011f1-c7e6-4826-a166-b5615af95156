<template>
  <div class="_fw relative-position">
    <template v-if="!passThrough || !through._id">
      <div class="tw-six font-1r">{{ coverage?.name }}</div>
      <q-separator class="q-my-sm"></q-separator>
      <div class="flex items-center">
        <template v-if="issuer._id || coverage?.carrierName">
          <div class="q-pr-sm font-3-4r tw-five">By:</div>
          <default-chip v-if="issuer._id" :model-value="issuer"></default-chip>
          <q-chip v-else color="ir-grey-2">
            <q-avatar color="white" v-if="carrierLogo?.url">
              <img :src="carrierLogo.url" alt=""/>
            </q-avatar>
            <span>{{ coverage.carrierName }}</span>
          </q-chip>
        </template>
        <q-chip v-if="!hideFor && coverage?.template" label="Public Offering"></q-chip>
      </div>
      <div class="row items-center q-py-sm">
        <type-chip :model-value="coverage?.type"></type-chip>
        <div class="q-px-sm tw-five font-3-4r">for</div>
        <covers-chip :model-value="coverage?.covered"></covers-chip>
      </div>
      <rate-table v-if="!coverage?.ichra" :model-value="coverage" :enrollment="enrollment" :def_age="def_age"
                  :def_key="def_key"></rate-table>
      <group-shop-card v-else v-bind="{ enrollment, coverage, plan, person }"></group-shop-card>
      <!--    <ichra-card v-else v-bind="{ enrollment, coverage, plan, person }"></ichra-card>-->
    </template>
    <template v-else>
      <policy-card
          v-if="through.acaPlan"
          :model-value="through"
          :enrollment="enrollment"
          :subsidy="subsidy"
          :aptc="aptc"
      ></policy-card>
      <coverage-card
          v-else
          :model-value="through"
          :enrollment="enrollment"
          :subsidy="subsidy"
      ></coverage-card>
    </template>
  </div>
</template>

<script setup>
  import CoversChip from 'components/coverages/cards/CoversChip.vue';
  import TypeChip from 'components/coverages/cards/TypeChip.vue';
  import DefaultChip from 'components/common/avatars/DefaultChip.vue';
  import RateTable from 'components/coverages/cards/RateTable.vue';
  // import IchraCard from 'components/coverages/ichra/cards/IchraCard.vue';
  import PolicyCard from 'components/enrollments/ichra/cards/PolicyCard.vue';
  import GroupShopCard from 'components/market/shop/enroll/GroupShopCard.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref, watch} from 'vue';
  import {useCoverages} from 'stores/coverages';
  import {useOrgs} from 'stores/orgs';
  import {useMarketplace} from 'stores/marketplace';
  import {erSubsidy} from 'components/market/shop/utils/subsidy';
  import {usePlans} from 'stores/plans';
  import {usePpls} from 'stores/ppls';
  import {useUploads} from 'stores/uploads';

  const store = useCoverages();
  const orgStore = useOrgs();
  const marketStore = useMarketplace();
  const coverageStore = useCoverages();
  const planStore = usePlans();
  const pplStore = usePpls();
  const uploadStore = useUploads();

  const props = defineProps({
    hideFor: Boolean,
    modelValue: { required: true },
    plan: { required: false },
    person: { required: false },
    enrollment: { required: false },
    passThrough: Boolean,
    def_age: Number,
    def_key: String
  })

  const { item: coverage } = idGet({
    value: computed(() => props.modelValue),
    store,
    params: ref({ runJoin: { add_files: ['carrierLogo'] } })
  })

  const { item: carrierLogo } = idGet({
    store: uploadStore,
    value: computed(() => coverage.value._fastjoin?.files?.carrierLogo || coverage.value.carrierLogo?.uploadId)
  })

  const { item: issuer } = idGet({
    value: computed(() => coverage.value?.issuer),
    store: orgStore
  })

  const { item: plan } = idGet({
    store: planStore,
    value: computed(() => props.enrollment?.plan)
  })

  const { item: person } = idGet({
    store: pplStore,
    value: computed(() => props.enrollment?.person)
  })

  const through = ref({});

  const loadThrough = async (tries = 0) => {
    if (coverage.value._id) {
      const { coverages, address, county } = props.enrollment || {};
      const config = (coverages || {})[coverage.value._id]
      if (config) {
        if (config.policy) {
          if (config.fullPolicy) through.value = config.fullPolicy;
          else through.value = await marketStore.get(config.policy, {
            runJoin: {
              get_plan: {
                id: config.policy,
                place: { countyfips: county?.fips, state: address?.region, zipcode: address?.postal },
                household: {
                  income: props.enrollment.householdIncome || 1000000,
                  people: Object.keys(props.enrollment.enrolled).map(a => props.enrollment.enrolled[a])
                }
              }
            }
          });
        } else if (config.individual_coverage) {
          through.value = config.fullCoverage || await coverageStore.get(config.individual_coverage);
        }
      }
    } else if (tries < 10) setTimeout(() => loadThrough(tries + 1))
  }
  watch(() => props.enrollment, (nv) => {
    if (nv && !through.value._id) loadThrough(0)
  }, { immediate: true })


  const { subsidy } = erSubsidy({
    plan,
    enrollment: computed(() => props.enrollment),
    coverageId: computed(() => coverage.value._id),
    person
  })

  const aptc = computed(() => (props.enrollment?.coverages || {})[coverage.value._id]?.aptc?.aptc)
</script>

<style lang="scss" scoped>


</style>

