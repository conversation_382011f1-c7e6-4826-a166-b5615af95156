import {AnyRef} from 'src/utils/types';
import {computed, Ref, ref, watch} from 'vue';
import {useMarketplace} from 'stores/marketplace';
import {useEnrollments} from 'stores/enrollments';
import {useCoverages} from 'stores/coverages';
import {getCoverageRate} from 'components/coverages/utils/display';

type Options = {
    enrollment?: AnyRef,
    coverage?: AnyRef
}

export const ichraPolicy = ({ enrollment, coverage }: Options = {}) => {
    const marketStore = useMarketplace();
    const eStore = useEnrollments();
    const coverageStore = useCoverages();
    const loading = ref(false);
    const config = computed(() => {
        return (enrollment.value?.coverages || {})[coverage.value?._id] || {}
    })
    const policyId = computed(() => {
        return config.value.policy || config.value.individual_coverage;
    })

    const policy:Ref<any> = ref({});
    const loadPolicy = async (refresh?:boolean) => {
        loading.value = true;
        try {
            const nv = policyId.value;
            const cfg = config.value;
            const ermt = enrollment.value || {}
            if (!refresh && cfg.fullPolicy?._id) return policy.value = cfg.fullPolicy;
            else if (!refresh && cfg.fullCoverage?._id) return policy.value = cfg.fullcoverage;
            if(cfg.individual_coverage){
                const p = await coverageStore.get(cfg.individual_coverage)
                if (p) {
                    const premium = getCoverageRate({ coverage: p, enrollment: enrollment.value })
                    policy.value = {...p, premium};
                    eStore.patch(ermt._id, {$set: { [`coverages.${coverage.value._id}.fullCoverage`]: policy.value }})
                }
            } else if(cfg.policy) {
                const place = {
                    countyfips: ermt.county?.fips,
                    state: ermt.county?.stateCode,
                    zipcode: ermt.address?.postal
                }
                let participants = (enrollment.value.coverages || {})[coverage.value._id].participants || [];
                if (!participants.length) participants = Object.keys(ermt.enrolled);
                const household = {
                    income: 1,
                    people: participants.map(a => ermt.enrolled[a])
                }
                const p = await marketStore.get(nv, {runJoin: {get_plan: {id: nv, household, place}}} as any)
                if (p) {
                    if(p.type !== 'aca'){
                        p.premium = getCoverageRate({ coverage: p, enrollment: enrollment.value });
                    }
                    policy.value = p;
                    eStore.patch(ermt._id, {$set: { [`coverages.${coverage.value._id}.fullPolicy`]: p }})
                }
            }

        } catch(error){
            console.log(`Error loading policy: ${error.message}`)
        } finally {
            loading.value = false;
        }
    }
    watch(policyId, (nv) => {
        if(nv && nv !== policy.value._id) loadPolicy()
    }, { immediate: true })

    return { policy, loadPolicy, policyId, loading}
}
