<template>
  <div class="_fw">
    <div class="_f_g">

      <template v-if="form.org">
        <div class="_form_grid _f_g_r">
          <div class="_form_label">For plan</div>
          <div class="q-pa-sm">
            <org-chip :model-value="form.org"></org-chip>
          </div>
        </div>
      </template>

      <template v-if="updateAvailable">
        <div class="_f_l _f_chip">Updates</div>
        <div class="_form_grid _f_g_r">
          <div class="_form_label">Update Available</div>
          <div class="q-pa-sm">
            <div class="font-7-8r">This coverage is attached to a public template and updates to the template were made.
              Check you can choose to adopt some or all of the updates.
            </div>
            <div class="q-pa-md">
              <q-btn label="Compare Changes" color="accent" push no-caps class="tw-six"
                     @click="syncDialog = true"></q-btn>
            </div>
          </div>
        </div>
        <common-dialog setting="large" v-model="syncDialog">
          <div class="_fw q-pa-md">
            <sync-templates
                :coverage="coverage"
                :template="parent"
                @close="syncDialog = false"
                @accept="acceptSync"
            ></sync-templates>
          </div>
        </common-dialog>
      </template>

      <!--      NAME-->
      <div class="_f_l _f_chip">Name</div>
      <div class="q-pa-sm">
        <q-input input-class="tw-six" placeholder="Coverage name..." v-model="form.name"
                 @blur="autoSave('name', form.name)"></q-input>
      </div>

      <!--      DESCRIPTION-->
      <div class="_f_l _f_chip">Description</div>
      <div class="q-pa-sm">
        <q-input
            autogrow
            placeholder="Short description..."
            maxlength="200"
            counter
            v-model="form.description"
            @blur="autoSave('description', form.description)"
        ></q-input>
      </div>

      <div class="_f_l _f_chip">Network Structure</div>
      <div class="q-pa-sm">
        <q-checkbox v-model="form.openNetwork" @update:model-value="autoSave('openNetwork', $event)"
                    label="Open Network"></q-checkbox>
        <template v-if="!form.openNetwork">
          <div class="_fw q-py-sm">
            <q-radio
                v-for="k of ['ppo', 'hmo', 'ppo', 'pos']"
                :key="`net-${k}`"
                v-model="form.plan_type"
                :val="k"
                :label="k.toUpperCase()" @update:model-value="autoSave('plan_type', $event)"
            ></q-radio>
          </div>
        </template>
      </div>


      <!--      COVERED-->
      <div class="_f_l _f_chip">Covered</div>
      <div class="q-pa-sm">
        <q-radio v-model="form.covered" label="Group" val="group"
                 @update:model-value="autoSave('covered', $event)"></q-radio>
        <q-radio v-model="form.covered" label="Individual" val="individual"
                 @update:model-value="autoSave('covered', $event)"></q-radio>
      </div>

      <!--      TYPE-->
      <div class="_f_l _f_chip">Class</div>
      <div class="q-pa-sm flex items-center">
        <div class="_fw">
          <type-picker v-model="form.type" @update:model-value="setType"></type-picker>
        </div>
        <q-slide-transition>
          <div v-if="form.type === 'hra'">
            <q-checkbox
                :model-value="form.ichra"
                label="Individual Coverage Reimbursement (ICHRA)"
                @update:model-value="setIchra"></q-checkbox>
          </div>
        </q-slide-transition>
        <q-checkbox :model-value="form.postTax" @update:model-value="togglePostTax" label="Post Tax"></q-checkbox>
        <q-slide-transition>
          <q-checkbox v-model="form.shop" v-if="form.postTax && form.covered === 'group'"
                      @update:model-value="autoSave('shop', $event)"
                      label="Allow Individual Shop Experience"></q-checkbox>
        </q-slide-transition>
      </div>

      <!--      ISTEMPLATE-->
      <template v-if="template || ['hs', 'mm'].includes(form.type)">

        <div class="_f_l _f_chip">Visibility</div>
        <div class="q-pa-sm">

          <q-checkbox v-if="template" label="Make Reusable Template" v-model="form.template"
                      @update:model-value="autoSave('template', $event)"></q-checkbox>
          <q-checkbox v-if="console" label="Make Publicly Available" v-model="form.public"
                      @update:model-value="autoSave('public', $event)"></q-checkbox>
        </div>

        <div class="_f_l _f_chip">Use In Simulations</div>
        <div class="q-pa-sm">
          <q-checkbox label="Use For Simulations" v-model="form.sim"
                      @update:model-value="autoSave('sim', $event)"></q-checkbox>
          <q-checkbox v-model="form.group_sim_only" @update:model-value="autoSave('group_sim_only', $event)"
                      v-if="form.sim" label="Only available to groups"></q-checkbox>
        </div>
      </template>
      <template v-if="console && form._id">
        <div class="_f_l _f_chip">Valuation</div>
        <div class="q-pa-sm">
          <div class="_form_grid">
            <div class="_form_label">Group Billing Discount</div>
            <div class="q-pa-sm">
              <money-input :disabled="!form.covered === 'individual'" dense filled v-model="form.listBillDiscount"
                           @update:model-value="autoSave('listBillDiscount')" prefix="" suffix="%"></money-input>
            </div>
            <template v-if="form.covered === 'group'">
              <div class="_form_label">Actuarial Value</div>
              <div class="q-pa-sm">
                <div class="row items-center q-py-xs">
                  <q-chip clickable v-for="(yr, i) in years()" :key="`yr-${i}`"
                          :color="`${av === yr ? 'p1' : 'ir-bg2'}`" @click="av = yr">
                  <span class="q-mr-sm alt-font">
                    <b>{{ yr }}</b>: <span
                      v-if="(form.av || {})[yr]?.value">{{ dollarString(form.av[yr].value, '', 2) }}</span><span v-else>N/A</span>
                  </span>
                    <q-icon v-if="!!(form.av || {})[yr]?.at" color="green" name="mdi-checkbox-marked">
                      <q-tooltip class="tw-six text-xxs">Confirmed</q-tooltip>
                    </q-icon>
                    <q-icon v-else name="mdi-checkbox-blank-outline">
                      <q-tooltip class="tw-six text-xxs">Unconfirmed (projected only)</q-tooltip>
                    </q-icon>
                  </q-chip>
                  <q-slide-transition>
                    <div class="_fw mw200 q-pt-md" v-if="av">
                      <money-input :label="`${av} Actuarial Value`" dense filled
                                   :model-value="(form.av || {})[av]?.value"
                                   @update:model-value="setForm(`av.${av}.value`, $event)"></money-input>
                    </div>
                  </q-slide-transition>
                </div>
              </div>
            </template>
          </div>
        </div>
      </template>


      <div class="_f_l _f_chip">Carrier</div>
      <div class="q-pa-sm">
        <div class="_form_grid">
          <div class="_form_label">Name</div>
          <div class="q-pa-sm">
            <q-input
                v-model="form.carrierName"
                @blur="autoSave('carrierName')"
            ></q-input>
          </div>
          <div class="_form_label">Logo</div>
          <div class="q-pa-sm">
            <carrier-logo-picker
                v-model="form.carrierLogo"
                @update:model-value="autoSave('carrierLogo', $event)"
            ></carrier-logo-picker>
          </div>
        </div>
      </div>

      <!--      PREMIUM-->
      <div class="_f_l _f_chip">Monthly Premium</div>
      <div class="_fw">
        <q-expansion-item group="0">
          <template v-slot:header>
            <premium-item :model-value="form"></premium-item>
          </template>

          <q-tab-panels
              class="_panel"
              animated
              v-model="addingRate"
              transition-next="jump-up"
              transition-prev="jump-down">
            <q-tab-panel class="_panel" :name="false">
              <div class="_fw _oxh">
                <premium-form v-model="form.premium" @update:model-value="autoSave('premium', $event)">
                  <template v-slot:rate-bottom>
                    <div class="_form_label">
                      Location Based Rates
                    </div>
                    <div class="q-pa-sm">
                      <q-chip v-if="form._id" color="transparent" clickable @click="addingRate = true">
                      <span
                          class="q-mr-sm">Location-Based Rates ({{
                          $possiblyPlural('Added', form.rates, '', '')
                        }})</span>
                        <q-icon color="red" name="mdi-map-marker"></q-icon>
                      </q-chip>
                      <div class="text-italic q-pa-sm" v-else>Finish creating coverage, then you can add location based
                        rates
                      </div>
                    </div>
                  </template>
                </premium-form>
              </div>
            </q-tab-panel>
            <q-tab-panel class="_panel" :name="true">
              <geo-rates :coverage="form"></geo-rates>
            </q-tab-panel>
          </q-tab-panels>

        </q-expansion-item>
      </div>
      <div class="_f_l _f_chip">Coinsurance</div>
      <div class="q-pa-sm">
        <q-list separator>
          <q-expansion-item group="coins" dense
                            :label="`Medical/Default Rate: ${dollarString(form.coinsurance?.amount, '', 2)}%`">
            <coins-form v-model="form.coinsurance" @update:modelValue="autoSave('coinsurance', $event)"></coins-form>
          </q-expansion-item>
          <q-expansion-item group="coins" dense v-for="(k, i) in Object.keys(form.coins || {})" :key="`coins-${i}`"
                            :label="`${form.coins[k].name || k}: ${dollarString(form.coins[k].amount, '', 2)}%`">
            <coins-form v-model="form.coins[k]" @update:modelValue="setCoins('coins', $event, k)"></coins-form>
          </q-expansion-item>
          <q-item clickable @click="form.coins = { ...form.coins, ['new_rate']: {}}">
            <q-item-section avatar>
              <q-icon name="mdi-plus"></q-icon>
            </q-item-section>
            <q-item-section>
              <q-item-label>Add Coinsurance Rate</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>

      </div>

      <div class="_f_l _f_chip">Copays</div>
      <div class="q-pa-sm">
        <q-list separator>
          <q-expansion-item group="copays" dense v-for="(k, i) in Object.keys(form.copays || {})" :key="`copay-${i}`"
                            :label="`${form.copays[k].name || k}: ${dollarString(form.copays[k].amount, '$', 0)}`">
            <coins-form v-model="form.copays[k]" @update:modelValue="setCoins('copays', $event, k)"></coins-form>
          </q-expansion-item>
          <q-item clickable @click="form.copays = { ...form.copays, ['new_rate']: {}}">
            <q-item-section avatar>
              <q-icon name="mdi-plus"></q-icon>
            </q-item-section>
            <q-item-section>
              <q-item-label>Add Copay</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </div>

      <!--        Max OOP-->
      <div class="_f_l _f_chip">Max OOP</div>
      <div class="_fw">
        <q-expansion-item group="0">
          <template v-slot:header>
            <limit-item :model-value="form.moop"></limit-item>
          </template>
          <limit-form oop v-model="form.moop" @update:model-value="autoSave('moop', $event)"></limit-form>
        </q-expansion-item>
      </div>

      <!--       Deductible-->
      <div class="_f_l _f_chip">Deductible</div>
      <div class="_fw _form_grid q-py-sm">
        <div class="_form_label">Preventive</div>
        <div class="row q-pa-sm">
          <q-checkbox
              v-model="form.preventive"
              label="Preventive services covered - no deductible"
              @update:model-value="autoSave('preventive', $event)"
          ></q-checkbox>
          <div class="q-py-sm"></div>
          <deductible-form
              v-model="form.deductible"
              @update:model-value="autoSave('deductible', $event)"
          ></deductible-form>
        </div>


      </div>
      <template v-if="form._id">
        <div class="_f_l _f_chip">Special Deductibles</div>
        <deductibles-form v-model="form.deductibles" :coverage-id="form._id" path="deductibles"></deductibles-form>
      </template>
      <div class="_f_l _f_chip">Network Incentives</div>
      <div class="q-pa-sm">
        <coverage-network-form
            :coverage="form"
            :plan="plan"
            v-model="form.networks"
            @update:model-value="autoSave('networks', $event)"
        ></coverage-network-form>
      </div>

      <div class="_f_l _f_chip">Other Settings</div>
      <div class="q-pa-sm">
        <div class="_form_grid">
          <div class="_form_label">Max Age</div>
          <div class="q-pa-sm">
            <money-input prefix="" v-model="form.maxAge" @blur="autoSave('maxAge',form.maxAge)"></money-input>
          </div>
          <div class="_form_label">Smoker Status</div>
          <div class="q-pa-sm">
            <q-input type="number" @blur="autoSave('monthsSinceSmoked', $event)"
                     v-model.number="form.monthsSinceSmoked" label="Months smoke-free for nonsmoker status"></q-input>
          </div>
          <div class="_form_label">
            Disabled Dependents
          </div>
          <div class="q-pa-sm">
            <q-checkbox :model-value="!!form.disability?.coverOverRequiredAge"
                        @update:model-value="setDisabledDependent" label="Cover over legal dependent age"></q-checkbox>
            <q-slide-transition>
              <money-input
                  :decimal="0" v-if="form.disability?.coverOverRequiredAge"
                  prefix="$"
                  v-model="form.disability.incomeLimit"
                  @update:model-value="autoSave('disability.incomeLimit', $event)"
              ></money-input>
            </q-slide-transition>
          </div>
        </div>
      </div>
      <div class="_f_l _f_chip">Service Area</div>
      <div class="q-pa-sm">
        <coverage-boundary adding :coverage="form"></coverage-boundary>
      </div>


      <div class="_f_l _f_chip">AI Access</div>
      <div class="q-pa-sm">
        <div class="font-3-4 q-py-smr">When we have plan details, we can store them in a semantic vector store so users
          can ask detailed questions and have highly specific responses. Contact us if you have data to add for this
          feature for this coverage option.
        </div>


        <ai-access-handler v-model="form.productDetailRef" @update:model-value="autoSave('productDetailRef', $event)"
                           :coverage="form" @remove="removeProductDetailRef"></ai-access-handler>
      </div>

      <div class="_f_l _f_chip">Documents</div>
      <div class="q-pa-sm">
        <multi-file-uploader
            @remove="removeDoc"
            v-model="form.documents"
            @update:model-value="autoSave('documents', $event)"
        ></multi-file-uploader>
      </div>

      <div class="_f_l _f_chip">Video</div>
      <div class="q-pa-sm">
        <video-form allow-url @remove="removeVideo" v-model="form.video"
                    @update:model-value="autoSave('video', $event)"></video-form>
      </div>

      <template v-if="form._id">
        <div class="_f_l _f_chip">Track Public Template</div>
        <div class="q-pa-sm">
          <div class="q-pb-sm font-7-8r">Many plan coverages are tracking a public template from the plan carrier. This
            allows you to keep changes in-sync easily.
          </div>

          <!--        ASSIGN TO TEMPLATE OR CHANGE TEMPLATE-->
          <template-picker :coverage-id="form._id" :model-value="form.fromTemplate"></template-picker>

        </div>
      </template>

      <template v-if="form._id && form.public && dirty">
        <div class="_f_l _f_chip">Publish Updates</div>
        <div class="q-pa-sm">
          <div class="q-pa-xs font-3-4r">This is a publicly usable template. You can push updates for those who have
            copied it can use to auto-update their copies
          </div>
        </div>
        <div class="q-pa-md row">
          <q-btn color="accent" push no-caps class="tw-six" @click="publishSync">
            <span class="q-mr-sm">Publish Updates</span>
            <q-spinner v-if="syncing" color="white"></q-spinner>
            <q-icon v-else name="mdi-refresh"></q-icon>
          </q-btn>
        </div>
      </template>

    </div>
    <div class="row justify-end q-px-md q-py-lg" v-if="dirty || !form._id">
      <q-btn
          push
          size="lg"
          class="_p_btn"
          no-caps
          :label="`Save ${form?._id ? 'Changes' : ''}`"
          :icon-right="!form?._id ? 'mdi-rocket-launch' : 'mdi-content-save'"
          @click="manualSave"
      ></q-btn>
    </div>

  </div>
</template>

<script setup>
  import LimitForm from 'components/plans/forms/premiums/LimitForm.vue';
  import MultiFileUploader from 'components/common/uploads/components/MultiFileUploader.vue';
  import TypePicker from 'components/coverages/cards/TypePicker.vue';
  import PremiumForm from 'components/coverages/forms/PremiumForm.vue';
  import PremiumItem from 'components/plans/cards/PremiumItem.vue';
  import LimitItem from 'components/plans/cards/LimitItem.vue';
  import GeoRates from 'components/coverages/rates/forms/GeoRates.vue';
  import MoneyInput from 'components/common/input/MoneyInput.vue';
  import CoverageNetworkForm from 'components/coverages/forms/CoverageNetworkForm.vue';
  import VideoForm from 'components/common/uploads/video/VideoForm.vue';
  import CarrierLogoPicker from 'components/coverages/utils/CarrierLogoPicker.vue';
  import CoverageBoundary from 'components/coverages/forms/CoverageBoundary.vue';
  import DeductibleForm from 'components/coverages/forms/DeductibleForm.vue';
  import DeductiblesForm from 'components/coverages/forms/DeductiblesForm.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import SyncTemplates from 'components/coverages/utils/sync/SyncTemplates.vue';
  import OrgChip from 'components/orgs/cards/OrgChip.vue';
  import CoinsForm from 'components/coverages/forms/CoinsForm.vue';
  import AiAccessHandler from 'components/coverages/ai/forms/AiAccessHandler.vue';
  import TemplatePicker from 'components/coverages/forms/TemplatePicker.vue';

  import {HForm, HSave} from 'src/utils/hForm';
  import {computed, ref} from 'vue';
  import {dependents} from 'components/plans/utils';

  import {useCoverages} from 'stores/coverages';

  import {$errNotify, $possiblyPlural, $successNotify, dollarString} from 'src/utils/global-methods';
  import {idGet} from 'src/utils/id-get';


  const store = useCoverages();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: { required: false },
    template: Boolean,
    addedBy: { required: false },
    plan: { required: false },
    console: { type: Boolean }
  })

  const addingRate = ref(false);
  const dirty = ref(false);
  const syncDialog = ref(false);
  const av = ref();

  const years = () => {
    const yr = Number(new Date().getFullYear());
    return [yr - 5, yr - 4, yr - 3, yr - 2, yr - 1, yr, yr + 1, yr + 2]
  }

  const formFn = (defs) => {
    return {
      type: 'mm',
      ...defs,
      premium: {
        rateByAge: {},
        multiDiscount: { 1: 1, 2: 1, 3: 1, 4: 1 },
        ...defs?.premium,
      }
    }
  }

  const { item: coverage } = idGet({
    store,
    value: computed(() => props.modelValue)
  })

  const { item: parent } = idGet({
    store,
    value: computed(() => coverage.value.fromTemplate)
  })
  const updateAvailable = computed(() => {
    if (!parent.value._id) return false;
    return new Date(parent.value.lastSync || '01/01/1980').getTime() > new Date(coverage.value.lastSync || '01/02/1980').getTime();
  })

  const params = { special_change: [], add_files: true }
  const manuallySaved = ref(false);
  const { form, save } = HForm({
    store,
    formFn,
    beforeFn: (val) => {
      if (props.addedBy) val.org = props.addedBy._id || props.addedBy;
      // if(!val.plan && props.plan) val.plan = props.plan._id || props.plan;
      return val;
    },
    params,
    afterFn: (val) => {
      if (manuallySaved.value) emit('update:model-value', val)
      manuallySaved.value = false
    },
    notify: true,
    successMessage: 'Saved Coverage',
    validate: true,
    vOpts: ref({
      type: { name: 'Type', v: ['notEmpty'] },
      covered: { name: 'Covered', v: ['notEmpty'] },
      name: { name: 'Name', v: ['notEmpty'] }
    }),
    value: coverage
  })


  const { autoSave, patchObj, setForm } = HSave({
    form,
    pause: ref(true),
    store,
    params,
    save,
    onChange: () => dirty.value = true,
    afterFn: () => dirty.value = false
  })

  const setCoins = (path, val, k) => {
    const newKey = val.name?.split(' ').join('_').toLowerCase()
    if ((form.value[path] || {})[k] && k !== newKey) {
      delete form.value.coins[k];
      form.value.coins = { ...form.value.coins, [newKey]: val }
      if (k !== 'new_rate') autoSave(`${path}.${k}`, val)
      autoSave(`${path}.${newKey}`, val)
    } else {
      form.value.coins[newKey] = val;
      autoSave(`${path}.${newKey}`, val)
    }
  }

  const manualSave = async () => {
    manuallySaved.value = true;
    if (!form.value._id) save();
    else {
      const patched = await store.patch(form.value._id, patchObj.value, params.value)
      if (patched._id) {
        patchObj.value = {}
        emit('update:model-value', patched)
        manuallySaved.value = false;
        dirty.value = false;
        $successNotify('Saved')
      }
    }
  }
  const setType = () => {
    autoSave('type');
  }

  const setDisabledDependent = (val) => {
    if (val) {
      const d = form.value.disability
      const obj = { ...d, coverOverRequiredAge: true };
      if (!obj.incomeLimit) obj.incomeLimit = dependents.disability.incomeLimit
      form.value.disability = obj;
      autoSave('disability', obj)
    }
  }

  const removeDoc = (file, i) => {
    form.value.documents.splice(i, 1);
    autoSave('documents');
  }

  const removeVideo = () => {
    if (form.value._id) store.patch(form.value._id, { $unset: { video: '' } })
    delete form.value.video;
  }

  const togglePostTax = (val) => {
    form.value.postTax = val;
    // if (!val) form.value.shop = false;
    if(val) form.value.ichra = false;
    autoSave('postTax', val)
    autoSave('shop', form.value.shop)
  }

  const setIchra = (val) => {
    form.value.ichra = val;
    if (val) {
      form.value.shop = true;
      togglePostTax(false);
    }
    autoSave('ichra', val)
  }

  const setCoinsurance = (val) => {
    form.value.coinsurance = val;
    autoSave('coinsurance', val)
  }

  const syncing = ref(false);
  const publishSync = async () => {
    let synced;
    syncing.value = true;
    try {
      synced = await store.patch(form.value._id, { $set: { lastSync: new Date() } })
    } catch (error) {
      $errNotify(error.message)
    } finally {
      syncing.value = false;
      if (synced._id) $successNotify('Update published')
    }
  }

  const acceptSync = async (path, val) => {
    autoSave('lastSync', new Date());
    if (val || val === 0 || val === false) {
      autoSave(path, val)
    } else patchObj.value.$unset = { ...patchObj.value.$unset, [path]: '' }
  }

  const removeProductDetailRef = () => {
    patchObj.value.$unset = { ...patchObj.value.$unset, productDetailRef: '' }
  }

</script>

<style lang="scss" scoped>

</style>
