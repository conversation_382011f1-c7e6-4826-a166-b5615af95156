<template>
  <div class="row justify-end">
    <q-chip color="ir-bg2" clickable :label="`Tax Filing Status: ${filingStatuses[filingAs]}`" icon-right="mdi-menu-down">
      <q-popup-proxy>
        <div class="w300 bg-white mw100 q-pa-sm">
          <q-list separator>
            <q-item v-for="(k, i) in Object.keys(filingStatuses)" :key="`sk-${i}`" clickable @click="filingAs = k">
              <q-item-section>
                <q-item-label class="text-xxs tw-six">{{ filingStatuses[k] }}</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </div>
      </q-popup-proxy>
    </q-chip>
  </div>

  <div class="__r">
    <div></div>
    <div>
      <q-chip dark dense square color="accent" class="tw-six text-xs">Job A</q-chip>
    </div>
    <div>
      <q-chip dark dense square color="primary" class="tw-six text-xs">Job B</q-chip>

    </div>
    <template v-for="(item, idx) in items" :key="`item-${idx}`">
      <div :class="`alt-font ${item.class} __b __${idx}_t`">
        <span class="__t">{{ item.label }}</span>
        <q-icon v-if="item.choices" clickable dense square class="q-ml-xs text-xs __ic" name="mdi-menu-down">
        </q-icon>
        <q-popup-proxy v-if="item.choices">
          <div class="w300 mw100 bg-white q-pa-sm">
            <q-list separator>
              <q-item v-for="(ch, index) in item.choices" :key="`choice-${idx}-${index}`" clickable
                      @click="item.select(ch)">
                <q-item-section>
                  <q-item-label class="text-xxs tw-six">{{ item.choiceFormat(ch) }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </div>
        </q-popup-proxy>
      </div>
      <div :class="`alt-font ${item.class} __${idx}_a`">
        <span class="__a">{{ dollarString(item.val1, '$', 0) }}</span>
      </div>
      <div :class="`alt-font ${item.class} __${idx}_b`">
        <span class="__b">{{ dollarString(item.val2, '$', 0) }}</span>
      </div>
    </template>

  </div>

</template>

<script setup>

  import {computed, ref, watch} from 'vue';
  import {filingStatuses, quickTaxTotal} from 'components/households/utils/tax-tables';
  import {compSources} from 'components/comps/utils';
  import {getEmployerFlexCredits} from 'components/plans/utils/calcs';
  import {dollarString} from 'symbol-syntax-utils';

  const props = defineProps({
    household: { required: false },
    cam: { required: false },
    enrollment: { required: false },
    plan: { required: false }
  })
  const coc = 8800;

  const budget = ref(80000);
  const costOfCare = ref(coc)
  const erContribution = ref(coc);
  const payOpts = [30000, 40000, 50000, 60000, 70000, 80000, 90000, 100000, 120000, 150000, 200000, 250000, 300000];
  const cocOpts = [1200, 2400, 3600, 4800, 6000, 7200, 10000, 15000, 20000]
  const filingAs = ref('s');

  watch(costOfCare, (nv, ov) => {
    if(nv !== ov) erContribution.value = nv || 0
  }, {immediate: true})

  const tax = computed(() => (quickTaxTotal({
    income: budget.value - costOfCare.value,
    hh_members: props.household?.members,
    filing_as: filingAs.value
  }) || {}).tax)

  const items = computed(() => {
    const taxablePay = (budget.value - costOfCare.value);
    const wageBudget = taxablePay * .9235;
    const FICA = Math.min(taxablePay, 176100) * .0765
    return [
      {
        label: 'Payroll Budget',
        val1: budget.value,
        val2: budget.value,
        choices: payOpts,
        choiceFormat: (v) => dollarString(v, '$', 0),
        select: (v) => budget.value = v,
        class: '__focus'
      },
      {
        label: 'ER Payroll Tax',
        val1: -1 * FICA,
        val2: -1 * FICA,
      },
      {
        label: 'Remaining Budget',
        val1: budget.value - FICA,
        val2: budget.value - FICA,
        class: '__focus1'
      },
      {
        label: `Plan Premium (${Object.keys(props.household?.members || {}).length + 1})`,
        val1: costOfCare.value,
        val2: costOfCare.value,
        choices: cocOpts,
        choiceFormat: (v) => dollarString(v, '$', 0),
        select: (v) => costOfCare.value = Math.min(grossPay, v),
        class: '__hp'
      },
      {
        label: 'Employer Pays',
        val1: erContribution.value,
        val2: 0,
        choices: [0, .1, .25, .5, .75, 1].map(a => costOfCare.value * a),
        choiceFormat: (v) => dollarString(v, '$', 0),
        select: (v) => erContribution.value = v,
        class: '__hp'
      },
      {
        label: 'You Pay',
        val1: Math.max(costOfCare.value - erContribution.value, 0),
        val2: costOfCare.value,
        class: '__hp __focus1'
      },
      {
        label: 'Gross Pay',
        val1: wageBudget,
        val2: wageBudget,
        class: '__focus'
      },
      {
        label: 'FICA Tax',
        val1: -1 * FICA,
        val2: -1 * FICA,
      },
      {
        label: 'Income Tax',
        val1: -1 * tax.value,
        val2: -1 * tax.value
      },
      {
        label: 'Take Home',
        val1: (wageBudget - FICA) - tax.value,
        val2: (wageBudget - FICA) - tax.value,
        class: '__final'
      }
    ]
  })

  watch(() => props.household, (nv) => {
    if (nv?.filingAs) filingAs.value = nv.filingAs
    if (nv?.members) {
      costOfCare.value = (((Object.keys(nv.members || {}).length || 0) + 1) * .5 + .5) * coc
    }
  }, { immediate: true })

  watch(() => props.cam, (nv) => {
    if (nv?.amount) {
      budget.value = compSources(nv, 'year').total * 1.0765 + erContribution.value;
    }
  }, { immediate: true })

  const setErContribution = (tries = 0) => {
    if (props.plan) {
      const er = getEmployerFlexCredits(props.plan, {
        enrollment: props.enrollment,
        totalIncome: budget.value - erContribution.value
      });
      if (er) {
        budget.value += erContribution.value - er * 12;
        erContribution.value = er * 12
      }
    } else if (tries < 11) setTimeout(() => setErContribution(tries + 1), 250)
  }

  watch(() => props.enrollment, (nv) => {
    if (nv?._id) setErContribution()
  }, { immediate: true })


</script>

<style lang="scss" scoped>
  .__r {
    width: 100%;
    display: grid;
    grid-template-columns: 1fr auto auto;
    font-size: var(--text-xs);
    align-items: center;
    justify-items: end;
    padding: 10px 10px;
    border-radius: 10px;

    > div {
      padding: 10px;
      border-bottom: solid 1px var(--ir-light);
      width: 100%;
      height: 100%;
      text-align: right;

      &:nth-child(1), &:nth-child(2), &:nth-child(3) {
        border-bottom: none;
      }

      &:nth-child(4), &:nth-child(7), &:nth-child(10), &:nth-child(13), &:nth-child(16), &:nth-child(19), &:nth-child(22), &:nth-child(25), &:nth-child(28), &:nth-child(31) {
        justify-self: start;
        text-align: left;
      }
    }
  }

  .__focus {
    font-weight: 600;
    .__a {
      color: var(--q-accent);
    }
    .__b {
      color: var(--q-primary);
    }
  }

  .__hp {
    background: var(--ir-bg2);
    color: var(--ir-text);
    font-weight: 600;

    .__a {
      color: var(--q-accent);
    }
    .__b {
      color: var(--q-primary);
    }
  }
  .__3_t {
    border-radius: 10px 0 0 0;
  }
  .__3_b {
    border-radius: 0 10px 0 0;
  }
  .__5_t {
    border-radius: 0 0 0 10px;
  }
  .__5_b {
    border-radius: 0 0 10px 0;
  }

  .__final {
    font-weight: 600;
    background: linear-gradient(180deg, var(--q-a1), var(--q-a2));

    .__a {
      color: var(--q-accent);
    }
    .__b {
      color: var(--q-primary);
    }
  }
  .__9_t {
    border-radius: 0 0 0 10px;
  }
  .__9_b {
    border-radius: 0 0 10px 0;
  }


</style>
