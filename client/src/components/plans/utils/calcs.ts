import {SessionStorage} from 'symbol-auth-client';

type Options = {
    enrollment: any
    totalIncome?:number,
    baseIncome?:number,
    contributions?:number,
    needs?:{preTax:number, postTax?:number, total?:number }
}
export const getEmployerFlexCredits = (plan:any, { enrollment, totalIncome, baseIncome, contributions, needs }:Options) => {
    if(!enrollment) return 0;
    const done = (val:number) => {
        if(plan?._id) SessionStorage.setItem(`er_benefit:${plan._id}`, val);
        return val;
    }
    const cont = plan?.employerContribution || {};
    const ec = cont['*'] || cont[enrollment.group] || { amount: 0 };
    if(!ec?.amount) return done(0);
    let amt = enrollment.type === 'family' ? Math.max(ec.family || 0, ec.amount) : ec.amount;
    /** adjust for the postTax employer contribution based on elected needs */
    if(cont.postTax && needs) {
        const postTaxRatio = (needs.postTax || 0) / (needs.total || 1);
        const preTaxRatio = 1 - postTaxRatio;

        // Blend full contribution with reduced contribution for post-tax
        const weight = (preTaxRatio) + (postTaxRatio * (cont.postTax/100));
        amt = amt * weight;
    }
    if (ec.type === 'flat') return done(amt);
    else if (ec.type === 'percent') {
        let max = 0;
        if(ec.percentType === 'cost'){
            max = (needs.total || 0) * (amt / 100)
        } else {
            const comp = (ec.includeExtras ? totalIncome || baseIncome || 0 : baseIncome || 0) / 12;
            max = (comp * (amt / 100))
        }
        if (ec.match){
            const minC = contributions || 0;
            //have to account for the fact that elective contributions include employer contributions
            return Math.max(0, Math.min(minC - max, max))
        }
        else return done(max);
    } else return done(0);
}

export const getElectedContributions = (enrollment?:any) => {
    let total = 0;
    let postTax = 0;
    let def = 0;
    const byPlan:any = {};
    for (const k in enrollment?.cafe || {}) {
        const { amount, optOut } = enrollment.cafe[k] || {amount: 0}
        if (amount && !optOut) {
            total += amount;
            if(k === 'cash') postTax += amount;
            if(k === 'def') def += amount;
            byPlan[k] = amount;
        }
    }
    if(enrollment?._id) SessionStorage.setItem(`elected:${enrollment?._id}`, total);
    return {total, postTax, def, preTax: total - postTax - def, byPlan};
}
