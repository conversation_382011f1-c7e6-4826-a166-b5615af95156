<template>
  <div class="_fw">
    <div class="q-pa-md font-1r">
      <div class="tw-six">Elected Benefit Summary</div>
      <default-chip :model-value="person"></default-chip>
      <q-chip color="ir-bg2" clickable @click="$copyTextToClipboard(person.email)">
        <q-icon color="primary" name="mdi-email"></q-icon>
        <span class="q-ml-sm">{{ person.email }}</span>
      </q-chip>
      <q-chip color="ir-bg2" clickable v-if="person.phone" @click="$copyTextToClipboard(person.phone.number.e164)">
        <q-icon color="accent" name="mdi-phone"></q-icon>
        <span class="q-ml-sm">{{ person.phone?.number?.national }}</span>
      </q-chip>
    </div>
    <q-tab-panels animated :model-value="!viewing" class="_panel">
      <q-tab-panel class="_panel" :name="true">

        <table class="alt-font">
          <thead>
          <tr>
            <th></th>
            <th>Plan</th>
            <th>Employer Cost</th>
            <th>Employee Cost</th>
            <th>Status</th>
          </tr>
          </thead>
          <tbody>
          <tr v-for="(cov, i) in c$.data" :key="`covt-${i}`" class="_hov cursor-pointer" @click="toggleEdits(cov)">
            <td>
              <q-avatar :color="typeColors[cov.type]" size="10px"></q-avatar>
            </td>
            <td>{{ cov.name }}</td>
            <td>{{ dollarString(contributions.byCoverage[cov._id]?.employer, '$', 2) }}</td>
            <td>{{ dollarString(contributions.byCoverage[cov._id]?.employee, '$', 2) }}</td>
            <td>
              <q-icon v-if="warnings[cov._id]" name="mdi-alert-box" color="orange" size="25px">
                <q-tooltip class="font-7-8r tw-six">{{ warnings[cov._id] }}</q-tooltip>
              </q-icon>
              <q-icon v-else color="green" name="mdi-check-circle" size="25px"></q-icon>
            </td>
          </tr>
          </tbody>
        </table>
      </q-tab-panel>
      <q-tab-panel class="_panel" :name="false">
        <div class="q-pa-sm">
          <q-btn dense flat icon="mdi-close" color="red" @click="toggleEdits(viewing)"></q-btn>
        </div>
        <div class="_fw q-pa-md relative-position">
          <div class="__loader flex flex-center" v-if="loading">
            <ai-logo size="30px" transparent></ai-logo>
          </div>
          <coverage-card
              :model-value="viewing"
              :enrollment="enrollment"
              pass-through
          ></coverage-card>

          <div v-if="viewing.shop || viewing.ichra" class="q-py-lg">
            <template v-if="configs[viewing._id]?.optOut">
              <div class="q-pa-lg font-1r">Chose post-tax {{ $ago(configs[viewing._id].optOut) }}</div>
            </template>
            <template v-else-if="configs[viewing._id]?.policy || configs[viewing._id]?.individual_coverage">
              <div class="q-pa-md font-1r tw-six">Confirm Enrollment Details</div>

              <div class="_form_grid">
                <template v-if="configs[viewing._id].policy">
                  <div class="_form_label">Policy ID</div>
                  <div class="q-pa-sm">
                    <q-input dense filled class="mw100 w400" v-model="confirm.policy"></q-input>
                  </div>
                  <div class="_form_label">APTC</div>
                  <div class="q-pa-sm">
                    <money-input dense filled prefix="$" :decimal="2" v-model="confirm.aptc" ></money-input>
                  </div>
                </template>

                <div class="_form_label">Household Income</div>
                <div class="q-pa-sm">
                  <money-input dense filled prefix="$" :decimal="2" v-model="confirm.income"></money-input>
                </div>

                <div class="_form_label">Premium</div>
                <div class="q-pa-sm">
                  <money-input dense filled prefix="$" :decimal="2" :model-value="confirm.premium"></money-input>
                </div>

                <div class="_form_label">Contributions</div>
                <div class="q-pa-sm">
                  <div class="font-7-8r">This is a <b>{{ viewing.postTax ? 'Post' : 'Pre' }}-Tax</b> coverage option
                  </div>
                  <div class="q-pt-md q-pb-sm tw-six font-7-8r">Summary of Benefit Costs</div>
                  <table class="__t">
                    <tbody>
                    <tr>
                      <td>Pre-Tax</td>
                      <td>{{ dollarString(contributions.needed.preTax, '$', 2) }}</td>
                    </tr>
                    <tr>
                      <td>Post-Tax</td>
                      <td>{{ dollarString(contributions.needed.postTax, '$', 2) }}</td>
                    </tr>
                    <tr class="tw-six">
                      <td>Total Cost</td>
                      <td>{{ dollarString(contributions.needed.total, '$', 2) }}</td>
                    </tr>
                    <tr class="text-ir-mid tw-six __ch">
                      <td>Changes</td>
                      <td></td>
                    </tr>
                    <tr class="__ch" v-if="configs[viewing._id].policy">
                      <td>APTC</td>
                      <td>
                        <span class="font-7-8r tw-six">{{changes.aptc > 0 ? '+' : '-'}}{{ dollarString(changes.aptc, '$', 2) }}</span>
                      </td>
                    </tr>
                    <tr class="__ch">
                      <td>Premium</td>
                      <td>{{changes.premium > 0 ? '+' : '-'}}{{ dollarString(changes.premium, '$', 2) }}</td>
                    </tr>
                    <tr class="__ch text-accent tw-six">
                      <td>New Total Cost</td>
                      <td>{{ dollarString(totalNeeds, '$', 2) }}</td>
                    </tr>
                    </tbody>
                  </table>

                  <div class="q-pt-md q-pb-sm tw-six font-7-8r">Summary of Contributions</div>

                  <table class="__t">
                    <tbody>
                    <tr>
                      <td>Employer</td>
                      <td>{{ dollarString(employerTotal, '$', 2) }}</td>
                    </tr>
                    <tr>
                      <td>Employee</td>
                      <td>{{ dollarString(employeeTotal - employerTotal, '$', 2) }}</td>
                    </tr>

                    <tr class="__total">
                      <td>Total</td>
                      <td>{{ dollarString(employeeTotal, '$', 2) }}</td>
                    </tr>

                    </tbody>
                  </table>

                </div>
                <div class="_form_label">Action</div>
                <div class="q-pa-sm _fw _oxs">
                  <div class="q-py-sm font-7-8r">
                    <div class="_fw" v-html="action.msg"></div>

                    <div class="_fw q-py-sm">Send them to <span class="text-accent tw-six _hov cursor-pointer" @click="$copyTextToClipboard(action.url)">{{ action.url }}</span>
                      to make changes.
                    </div>

                    <div class="_fw tw-six">You should still confirm these policy details first so {{ action.name }}'s changes
                      are confirmed as correct.
                    </div>
                  </div>
                </div>

                <div class="_form_label">Confirmation</div>
                <div class="q-pa-sm">
                  <div class="q-pa-md row justify-end" v-if="!confirming">
                    <q-btn color="accent" push no-caps label="Confirm" icon-right="mdi-check-circle"
                           @click="confirming = true"></q-btn>
                  </div>

                  <q-slide-transition>
                    <div class="_fw" v-if="confirming">
                      <q-checkbox class="q-my-md" v-model="confirmed" :label="`I, ${lPerson.name}, attest to the policy details provided - especially the premium and advance premium tax credit (APTC) listed.`"></q-checkbox>

                      <div class="_fw q-py-md">
                        <file-object-form @update:model-value="addFiles" v-model="files"></file-object-form>
                      </div>

                      <div class="q-pa-md row justify-end">
                        <q-btn no-caps flat @click="confirming = false">
                          <span class="q-mr-sm">Cancel</span>
                          <q-icon color="red" name="mdi-close"></q-icon>
                        </q-btn>
                        <q-btn no-caps flat :disable="!confirmed" @click="finalizeConfirmation">
                          <span class="q-mr-sm">Confirm</span>
                          <q-icon color="green" name="mdi-check-circle"></q-icon>
                        </q-btn>
                      </div>
                    </div>
                  </q-slide-transition>
                </div>
              </div>


            </template>
          </div>
        </div>

      </q-tab-panel>

    </q-tab-panels>

  </div>
</template>

<script setup>
  import CoverageCard from 'components/coverages/cards/CoverageCard.vue';
  import MoneyInput from 'components/common/input/MoneyInput.vue';
  import DefaultChip from 'components/common/avatars/DefaultChip.vue';
  import FileObjectForm from 'components/common/uploads/utils/FileObjectForm.vue';
  import AiLogo from 'src/utils/icons/AiLogo.vue';

  import {useEnrollments} from 'stores/enrollments';
  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {HFind} from 'src/utils/hFind';
  import {useCoverages} from 'stores/coverages';
  import {$ago, $copyTextToClipboard, $errNotify} from 'src/utils/global-methods';
  import {enrollmentContributions} from 'components/enrollments/utils';
  import {usePlans} from 'stores/plans';
  import {usePpls} from 'stores/ppls';
  import {typeColors} from 'components/coverages/utils/types';
  import {_set, dollarString} from 'symbol-syntax-utils';
  import {loginPerson} from 'stores/utils/login';

  const { person: lPerson } = loginPerson()

  const erStore = useEnrollments()
  const coverageStore = useCoverages();
  const planStore = usePlans();
  const pplStore = usePpls();

  const emit = defineEmits(['done']);
  const props = defineProps({
    modelValue: { required: true }
  })

  const { item: enrollment } = idGet({
    value: computed(() => props.modelValue),
    store: erStore
  })
  const { item: plan } = idGet({
    store: planStore,
    value: computed(() => enrollment.value.plan)
  })

  const { item: person } = idGet({
    store: pplStore,
    value: computed(() => enrollment.value.person)
  })

  const viewing = ref();
  const confirming = ref(false);

  const coverageIds = computed(() => Object.keys(enrollment.value.coverages || {}));

  const configs = computed(() => enrollment.value.coverages || {})

  const { h$: c$ } = HFind({
    store: coverageStore,
    limit: computed(() => coverageIds.value.length),
    params: computed(() => {
      return {
        query: {
          _id: { $in: coverageIds.value }
        }
      }
    })
  })

  const lastView = ref();
  const confirm = ref({});
  const confirmed = ref(false);
  const files = ref({});
  const loading = ref(false);

  const offExchange = computed(() => configs.value[viewing.value._id]?.fullPolicy?.off_exchange)
  const changes = computed(() => {
    if (!viewing.value?._id || !configs.value[viewing.value._id]) return {};
    const config = configs.value[viewing.value._id]
    return {
      aptc: offExchange.value ? 0 : (confirm.value.aptc || 0) - (config.aptc?.aptc || 0),
      premium: (confirm.value.premium || 0) - (config.premium || 0),
      income: (confirm.value.income || 0) - (config.aptc?.income || 0),
      policy: confirm.value.policy
    }
  })


  const toggleEdits = (cov) => {
    const id = cov._id;
    if (viewing.value?._id === id) {
      lastView.value = id;
      confirming.value = false;
      return viewing.value = undefined;
    }
    if (lastView.value !== id) confirm.value = {}
    viewing.value = cov;
    const config = configs.value[id]
    confirm.value.policy = config.policy || config.individual_coverage;
    confirm.value.aptc = offExchange.value ? 0 : config.aptc?.aptc;
    confirm.value.premium = config.premium;
    confirm.value.income = config.aptc?.income;
    files.value = config.files || {}
  }

  const finalizeConfirmation = async () => {
    const patchObj = {};
    const config = configs.value[viewing.value._id];
    if(confirm.value.aptc !== config.aptc?.aptc) patchObj[`coverages.${viewing.value._id}.aptc.aptc`] = offExchange.value ? 0 : confirm.value.aptc;
    if(confirm.value.premium !== config.premium) patchObj[`coverages.${viewing.value._id}.premium`] = confirm.value.premium;
    if(confirm.value.income !== config.aptc?.income) patchObj[`coverages.${viewing.value._id}.aptc.income`] = confirm.value.income;
    if(confirm.value.policy !== config.policy) patchObj[`coverages.${viewing.value._id}.policy`] = confirm.value.policy;
    patchObj[`coverages.${viewing.value._id}.confirmedAt`] = new Date();
    patchObj[`coverages.${viewing.value._id}.confirmedBy`] = lPerson.value._id;
    patchObj[`coverages.${viewing.value._id}.confirmData`] = {
      aptc: confirm.value.aptc,
      premium: confirm.value.premium,
      income: confirm.value.income,
      policy_id: confirm.value.policy
    }
    patchObj.status = 'complete'
    loading.value = true;
    try {
      let inStore = {};
      for(const k in patchObj){
        inStore = _set(inStore, k, patchObj[k])
      }
      erStore.patchInStore(inStore)
      await erStore.patch(enrollment.value._id, { $set: patchObj })
    } catch(e){
      $errNotify(`Error saving: ${e.message}`)
    } finally {
      loading.value = false;
      emit('done')
      toggleEdits(viewing.value)
    }
  }


  const addFiles = (val) => {
    erStore.patch(enrollment.value._id, { $set: { [`coverages.${viewing.value._id}.files`]: val } })
  }

  const { contributions } = enrollmentContributions({
    enrollment,
    plan,
    person
  })

  const warnings = computed(() => {
    const obj = {};
    for (const k in configs.value) {
      const config = configs.value[k];
      if (config.policy && !config.confirmedAt) obj[k] = 'Confirm Policy Details';
      if (config.policy && (Math.abs((config.premium - (config.policy ? config.aptc?.aptc || 0 : 0)) - (contributions.value.byCoverage || {})[k]?.needed) > .01)) obj[k] = 'Contributions don\'t match cost';
    }
    return obj;
  })

  const totalNeeds = computed(() => (contributions.value.needed.total || 0) - changes.value.aptc + changes.value.premium)

  const employeeTotal = computed(() => {
    let total = 0;
    for (const k in enrollment.value.cafe || {}) {
      total += enrollment.value.cafe[k].amount || 0;
    }
    return total;
  })
  const employerTotal = computed(() => {
    return (contributions.value.employer.coverages || 0) + (contributions.value.employer.cafe || 0);
  })

  const getRootDomain = () => {
    const origin = window.location.origin;
    const spl = origin.split('.');
    const extensions = ['com', 'co', 'net', 'ai', 'org', 'io', 'tech', 'info', 'app']
    for (let i = 0; i < spl.length; i++) {
      console.log('check split', spl[i], extensions.includes(spl[i]));
      if (extensions.includes(spl[i])) return 'https://' + spl[i - 1] + '.' + spl[i];
    }
    return origin;
  }

  const action = computed(() => {
    const conts = employeeTotal.value;
    const def = totalNeeds.value - conts;
    const url = `${getRootDomain()}/enroll/${enrollment.value._id}/benefits`
    const name = person.value.firstName || person.value.name.split(' ')[0]
    const msg = def > 0 ? `${name} needs to increase contributions by <b>${dollarString(def, '$', 2)}</b>.` : def < 0 ? `${name} needs to decrease contributions by <b>${dollarString(Math.abs(def), '$', 2)}</b>.` : `${name} has the correct contributions set.`;
    return {
      msg,
      url,
      name
    }
  })


</script>

<style lang="scss" scoped>

  table {
    font-family: var(--alt-font);
    width: 100%;
    border-collapse: collapse;

    th, td {
      padding: 5px 10px;
      text-align: left;
    }

    td {
      border-bottom: solid .3px var(--ir-light);
    }

    tr {
      &:last-child {
        td {
          border-bottom: none;
        }
      }
    }
  }

  .__t {
    tr {
      td {
        &:first-child {
          width: 80%;
        }

        &:last-child {
          text-align: right;
        }
      }
    }
  }

  .__ch {
    td {
      background: var(--q-a0);
    }
  }

  .__total {
    td {
      background: var(--q-p0);
      font-weight: 600;
      color: var(--q-primary);
    }
  }

  .__loader {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(4px);
    z-index: 10;
  }
</style>
