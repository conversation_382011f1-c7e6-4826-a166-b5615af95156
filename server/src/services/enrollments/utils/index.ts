import {HookContext} from '../../../declarations.js';
import {CoreCall} from 'feathers-ucan';
import {compSources} from '../../comps/utils/index.js';
import {_get, getStateCode, dollarString} from '../../../utils/index.js';
import {getCmsPerson} from '../../marketplace/cms/index.js';
import {getAptc} from '../../marketplace/utils/hooks.js';

export const getAge = (dob: string) => {
    const today = new Date(); // Get today's date
    const birthDate = new Date(dob); // Convert input to a Date object

    let age = today.getFullYear() - birthDate.getFullYear(); // Calculate the year difference

    // Adjust age if the birthday hasn't occurred yet this year
    const hasHadBirthday =
        today.getMonth() > birthDate.getMonth() ||
        (today.getMonth() === birthDate.getMonth() && today.getDate() >= birthDate.getDate());

    if (!hasHadBirthday) {
        age--;
    }

    return age;
}
export const cmsDateFormat = (date: any) => {
    let dt = date;
    try {
        dt = new Date(date).toISOString().split("T")[0]
    } catch (e: any) {
        console.log(`Error creating cms date: ${e.message}`)
    }
    return dt;
}
export const convertCmsPerson = (p: any) => {
    const dob = cmsDateFormat(p.dob);
    const age = p.age || p.age === 0 ? p.age : getAge(p.dob);
    return {
        age,
        dob,
        child: !!(p.dependent || p.relation === 'child' || age < 18),
        smoker: !!(p.smoker || (p.monthsSinceSmoked || 100) < 12),
        gender: p.gender?.toLowerCase() === 'male' ? 'Male' : 'Female'
    }
}
export const getElectedContributions = (enrollment: any) => {
    let total = 0;
    let postTax = 0;
    let def = 0;
    const byPlan: any = {};
    for (const k in enrollment?.cafe || {}) {
        const {amount, optOut} = enrollment.cafe[k] || {amount: 0}
        if (amount && !optOut) {
            total += amount;
            if (k === 'cash') postTax += amount;
            if (k === 'def') def += amount;
            byPlan[k] = amount;
        }
    }
    return {total, postTax, def, preTax: total - postTax - def, byPlan};
}

type EcOptions = {
    enrollment: any
    totalIncome?: number,
    baseIncome?: number,
    contributions?: number,
    needs?: { preTax: number, postTax?: number, total?: number }
}


export const getEmployerFlexCredits = (plan: any, {
    enrollment,
    needs,
    totalIncome,
    baseIncome,
    contributions
}: EcOptions) => {
    if (!enrollment) return 0;
    const cont = plan?.employerContribution || {};
    const ec = cont['*'] || cont[enrollment.group] || {amount: 0};
    if (!ec?.amount) return 0;
    let amt = enrollment.type === 'family' ? Math.max(ec.family || 0, ec.amount) : ec.amount;
    if(ec.type === 'percent'){
        const comp = (ec.includeExtras ? totalIncome || baseIncome || 0 : baseIncome || 0) / 12;
        const max = (comp * (amt / 100))
        if (ec.match) {
            const minC = contributions || 0;
            //have to account for the fact that elective contributions include employer contributions
            amt = Math.max(0, Math.min(minC - max, max))
        } else amt = max;
    }
    /** adjust for the postTax employer contribution based on elected needs - only considering where there aren't enough pretax needs for the employer to contribute toward */
    if (cont.postTax && needs) {
        const leftToPostTax = amt - (needs.preTax || 0)
        if(leftToPostTax > 0){
            const postTaxRatio = Math.max(1, leftToPostTax / amt)
        }
        const preTaxRatio = 1 - postTaxRatio;

        // Blend full contribution with reduced contribution for post-tax
        const weight = (preTaxRatio) + (postTaxRatio * (cont.postTax / 100));
        amt = amt * weight;
    }

    return amt;
}

type PremiumObjOpts = {
    coverage: any,
    enrollment?: any,
    rate?: any
}
export const getPremiumObj = ({coverage, enrollment, rate}: PremiumObjOpts) => {
    const def = {}
    // const {baseDefault} = coverage.premium || {};
    /**Check for location-based rates*/
    let noLocal = true;
    if (enrollment && rate) {
        const stateMatch = rate?.state !== getStateCode(enrollment?.address?.region);
        noLocal = !stateMatch;
    }
    if (noLocal) {
        return coverage.premium || def;
    } else {
        const zip = (coverage.address?.postal || '').slice(0, 5);
        const stateDefault = rate.premium?.baseDefault;
        if (!zip && stateDefault) return rate.premium;
        const zipMatches = (rate.areas || []).filter((a: any) => (a.zips || []).includes(zip))
        if (zipMatches?.length) return zipMatches[0].premium || stateDefault ? rate.premium : def;
        else if (stateDefault) return rate.premium;
        return def
    }
};

declare type Rate = {
    [key: string | number]: number
}
declare type FixedRates = {
    self: Rate,
    plus_spouse: Rate,
    plus_child: Rate,
    family: Rate
}

export const getFixedRatePremium = ({fixedRates, key, oldest_age}: {
    fixedRates: FixedRates,
    key: string,
    oldest_age: number
}): number => {
    return _get(fixedRates, [oldest_age || 65, key]) || _get(fixedRates, [oldest_age || 65, key.split('__')[0]]) || 0
}

type FixedRateKeyArgs = {
    enrollment: any,
    enrolled_in_coverage: Array<string>
    enrolled_by_id: { [key: string]: any }
}

type Enrolled = Array<{ age: number, relation: string }>
/** take an enrollment and return the proper age banded rate key based on the enrollment in a coverage */
export const getFixedRateKey = ({enrolled, def_age, def_key}: {
    def_age?: number,
    def_key?: string,
    enrolled: Enrolled
}): {
    key: string,
    oldest_age: number
} => {
    const count = enrolled.length;
    if (!count) return {key: def_key || 'single', oldest_age: def_age || 64}
    let key = def_key || 'single';
    let oldest_age = def_age || 64;
    let spouse;
    let self;
    //loop through enrollees to determine the correct rate key
    if (count > 1) {
        for (let i = 0; i < count; i++) {
            const ee = enrolled[i];
            if (ee) {
                oldest_age = Math.min(oldest_age, ee.age);
                if (ee.relation === 'spouse') {
                    spouse = true;
                    if (count === 2) {
                        key = 'plus_spouse'
                        break;
                    } else {
                        key = 'family';
                        break;
                    }
                } else if (ee.relation !== 'self') {
                    if (!spouse && self) key = `plus_child__${Math.max(count, 3)}`
                    else if (!self) key = `plus_child__${Math.max(count, 3)}`
                    else key = 'family'
                } else {
                    self = true;
                    if (spouse) {
                        if (count === 2) {
                            key = 'plus_spouse'
                            break;
                        } else {
                            key = 'family';
                            break;
                        }
                    } else {
                        if (count === 2) {
                            key = 'plus_child';
                            break;
                        } else key = `plus_child__${Math.max(count, 3)}`
                    }
                }
            }
        }
    } else oldest_age = enrolled[0]?.age || def_age || 64
    return {key, oldest_age}
}


type CoverageRateOpts = {
    coverage: any,
    enrollment?: any,
    rate?: any,
    def_age?: number,
    def_key?: string,
    enrolled?: Enrolled
}

export const simRelations = (arr: Array<any>): Array<any> => {
    arr.sort((a, b) => b.age - a.age)
    if (arr[1]) {
        const diff = Math.abs(arr[1].age - arr[0].age);
        if (arr[0].relation === 'self') arr[1].relation = diff < 16 ? 'spouse' : 'child'
        else if (arr[1]?.relation === 'self') {
            arr[0].relation = diff < 16 ? 'spouse' : 'child';
        } else {
            arr[0].relation = 'self'
            arr[1].relation = diff < 16 ? 'spouse' : 'child'
        }
    } else if (!arr[0].relation) arr[0].relation = 'self'
    for (let i = 2; i < arr.length; i++) {
        if (!arr[i].relation) arr[i].relation = 'child'
    }
    return arr;
}

export const getEnrolled = ({enrolled, enrollment, coverage}: any) => {
    let enRolled: any = enrolled || [];
    if (!enRolled.length) {
        const arr: Enrolled = []
        const participants = (_get(enrollment, `coverages.${coverage?._id}.participants`) || Object.keys(enrollment?.enrolled || {}) || []) as Array<string>
        const ages: any = [];
        for (let i = 0; i < participants.length; i++) {
            const {age, relation} = (enrollment.enrolled || {})[participants[i]] || {};
            if (age || age === 0) {
                ages.push(i);
                arr.push({age, relation})
            }
        }

        if (arr.length) enRolled = arr;
    }
    return enRolled
}
/**
 * take enrolled and calculate premium accordingly
 */
export const getCoverageRate = (args: CoverageRateOpts): number => {
    const {coverage, enrollment, rate, def_age, def_key, enrolled} = args
    if (coverage.acaPlan || typeof coverage.premium === 'number') return coverage.premium;
    let enRolled: any = getEnrolled({enrolled, enrollment, coverage});
    if (enRolled.length && enRolled.filter(a => !a.relation).length) enRolled = simRelations(enRolled);
    const l = enRolled.length;
    if (l || def_key) {
        const premium: any = getPremiumObj({coverage, enrollment, rate}) || {};
        const frKey = getFixedRateKey({
                enrolled: enRolled,
                def_age,
                def_key
            }
        )
        if (premium?.flatPremium && premium.flatPremium[frKey.key] && (premium.rateType === 'flatPremium' || !premium.rateType)) return premium.flatPremium[frKey.key];
        if (premium.fixedRates && (premium.rateType === 'fixedRates' || !premium.rateType)) return getFixedRatePremium({
            fixedRates: premium.fixedRates, ...frKey
        })


        const enrByKey = {
            'single': [{}],
            'plus_spouse': [{}, {}],
            'plus_child': [{}, {age: 10}],
            'plus_child__2': [{}, {age: 10}, {age: 10}],
            'plus_child__3': [{}, {age: 10}, {age: 10}, {age: 10}]
        }
        if (!enRolled?.length && def_key) enRolled = enrByKey[def_key]
        let baseRate = 0;
        const {rateByAge = {0: 0}} = premium;
        for (const ee of enRolled || []) {
            baseRate += (rateByAge[ee.age || 20] || 0)
        }
        if (premium.multiDiscount) {
            let discount = 1;
            const max = enRolled.length
            for (const k in premium.multiDiscount) {
                if (Number(k) <= max) {
                    if ((premium.multiDiscount[k]) < discount) discount = Math.max(premium.multiDiscount[k], .25);
                }
            }
            baseRate = baseRate * discount;
        }
        return baseRate

    } else return 0;
}

export const getEnrollmentContributions = (enrollment: any) => {
    return async (context: HookContext) => {
        if (context.params.skip_hooks) return context;
        const plan = await new CoreCall('plans', context, {skipJoins: true}).get(enrollment.plan);
        const cams = await new CoreCall('cams', context).find({
            query: {person: enrollment.person, $limit: 10},
            runJoin: {'cams_groups': plan.groups}
        });
        /**ensure all elected coverages are offered by the plan in the current setup - to prevent aggregation errors*/
        const filteredCoverageKeys = Object.keys(enrollment.coverages).filter(a => !!plan.coverages[a]);
        const coverages = await new CoreCall('coverages', context, {skipJoins: true}).find({
            query: {
                _id: {$in: filteredCoverageKeys},
                $limit: filteredCoverageKeys.length
            }
        });
        let base = 0;
        let ttl = 0;
        for (let i = 0; i < cams.data.length; i++) {
            const {amount = 0, total = 0} = compSources(cams.data[i] as any, 'year')
            base += amount;
            ttl += total;
        }

        let needTotal = 0;
        let postTax = 0;
        let employerCoverages = 0;
        const byCoverage: any = {};
        const rates = await new CoreCall('rates', context).find({
            query: {
                $limit: coverages.data.length + 5,
                state: getStateCode(enrollment.address.region),
                coverage: {$in: coverages.data.map(a => a._id)}
            }
        })
        const coverageById:any = {}
        const postByCoverageId:any = {}
        for (let i = 0; i < coverages.data.length; i++) {
            const cov = coverages.data[i]
            coverageById[cov._id] = cov;
            let num = 0;
            const config = enrollment.coverages[cov._id] || {}
            let isPost = config.postTax;
            if (cov.shop || cov.ichra) {

                if(!c.ichra) isPost = true;

                /** single function for getting cms api household and place data + returning the shop record for this enrollment */
                const getCmsData = async () => {
                    const shop = await new CoreCall('shops', context).get(enrollment.shop)
                        .catch(err => console.log(`could not get shop for enrollment in totaling contributions. enrollmentId: ${enrollment._id}: ${err.message}`))
                    const place = {
                        countyfips: enrollment.county?.fips,
                        state: enrollment.county?.stateCode,
                        zipcode: enrollment.address?.postal
                    }
                    const household = {
                        income: shop?.stats.income || 1,
                        people: [getCmsPerson({
                            age: shop?.stats.age,
                            child: false,
                            smoker: shop ? !!shop.stats.smoker : Object.keys(enrollment.enrolled || {}).some(a => (enrollment.enrolled[a].monthsSinceSmoked || Infinity) < 12),
                            gender: shop?.stats.gender || (enrollment.enrolled || {})[enrollment.person]?.gender
                        }), ...(shop ? (shop.stats.people || []).filter(a => !a.inactive).map(convertCmsPerson) : Object.keys(enrollment.enrolled || {}).map(k => convertCmsPerson((enrollment.enrolled || {})[k])))]
                    }
                    return {shop, household, place}
                }

                /** get the aptc from config or fetch it if it is missing */
                const get_aptc = async ({household, place, shop}: any) => {
                    if (cov.ichra) return 0;
                    if (config.aptc?.aptc) return config.aptc.aptc;
                    else {
                        const silver_ids = (shop?.coverages || []).filter(a => a.metal?.includes('ilver'));
                        let silver_plans = [];
                        if (silver_ids.length) {
                            const fetched_silvers = await new CoreCall('marketplace', context).find({
                                query: {},
                                find_by_id: {id: silver_ids, household, place}
                            })
                                .catch(err => {
                                    console.error(`Error getting aptc for enrollment contribution totaling: ${err.message}`)
                                    return {data: []}
                                })
                            silver_plans = fetched_silvers.data;
                        }
                        return getAptc({household, place, silver_plans})

                    }
                }
                // /** ideally the last calculated individual plan premium was saved to enrollment when the options were elected */
                // REMOVED THIS FOR NOW BECAUSE THE LOGIC OF SUBTRACTING THE APTC IS TOO REDUNDANT
                // if (config.premium) {
                //     num = config.premium
                //     if (config.policy) {
                //         const {household, place, shop} = await getCmsData()
                //         const aptc = await get_aptc({household, place, shop})
                //         num = Math.max(0, num - (aptc || 0))
                //     }
                // }
                /** find individual coverage option and get premium if none was included */
                if (config.individual_coverage) {
                    let cov2 = config.fullCoverage
                    if (!cov2 || cov2._id !== config.individual_coverage) {
                        cov2 = await new CoreCall('coverages', context).get(enrollment.coverages[cov._id].individual_coverage)
                            .catch(err => console.log(`Error getting individual coverages for totaling premium: ${err.message}`))
                    }
                    if (cov2) {
                        num = getCoverageRate({coverage: cov2, enrollment})
                        cov2.premium = num;
                        config.fullCoverage = cov2;
                        config.premium = num;
                        enrollment.coverages[cov._id].premium = num;
                        enrollment.coverages[cov._id].fullCoverage = cov2;
                        new CoreCall('enrollments', context)._patch(enrollment._id, {$set: {[`coverages.${cov._id}.premium`]: num, [`coverages.${cov._id}.fullCoverage`]: cov2}}, {
                            skip_hooks: true,
                            admin_pass: true
                        })
                            .catch(err => console.log(`Error updating enrollment with calculated coverage premium. Enrollment id ${enrollment._id}. Err: ${err.message}`))
                    }
                    /** find marketplace policy and get premium if none was included */
                } else if (config.policy && enrollment.shop) {
                    /** get shop because thats the source of most concise household data */
                    let policy = config.fullPolicy
                    const {shop, household, place} = await getCmsData()

                    if (!policy || policy._id !== config.policy) {

                        policy = await new CoreCall('marketplace', context).get(enrollment.coverages[cov._id].policy, {
                            runJoin: {
                                get_plan: {
                                    id: enrollment.coverages[cov._id].policy,
                                    household,
                                    place
                                }
                            }
                        })
                            .catch(err => console.log(`Error getting cms marketplace plan in enrollment contribution totaling: ${err.message}`))
                        /** if premium is found, use it and patch enrollment - which should have already had this */
                        if(policy.type !== 'aca'){
                            policy.premium = getCoverageRate({coverage: policy, enrollment})
                        }
                        config.fullPolicy = policy;
                    }
                    if (policy?.premium) {
                        const isAca = policy.type === 'aca';
                        if(!isAca) {
                            policy.premium = getCoverageRate({coverage: policy, enrollment})
                        }
                        const aptc = !cov.ichra && isAca && !policy.off_exchange ? await get_aptc({household, place, shop}) : 0
                        num = policy.premium - (aptc || 0)
                        config.fullPolicy = policy;
                        config.premium = num;
                        enrollment.coverages[cov._id].premium = num;
                        enrollment.coverages[cov._id].fullPolicy = policy;
                        new CoreCall('enrollments', context)._patch(enrollment._id, {$set: {[`coverages.${cov._id}.premium`]: num, [`coverages.${cov._id}.fullPolicy`]: policy}}, {
                            skip_hooks: true,
                            admin_pass: true
                        })
                            .catch(err => console.log(`Error updating enrollment with calculated policy premium. Enrollment id ${enrollment._id}. Err: ${err.message}`))
                    }

                }

            } else {
                const rate = rates.data.filter(a => a.coverage === cov._id)[0]
                num = getCoverageRate({coverage: cov, enrollment, rate});
            }

            /** Category ID: employer_contributions */
            /** Employer contribution single vs family */
            const ec = config.employerContribution || {};
            const eType = enrollment?.type || 'single'
            let ecTotal = eType === 'family' && enrollment.coverages[cov._Id].pariticpants?.length > 1 ? ec.family : ec.single
            if (ec.type === 'percent') {
                ecTotal = (ecTotal / 100) * num
            }
            byCoverage[cov._id] = {needed: isNaN(num) ? 0 : Number(num || 0)};

            needTotal += num;
            if(!isPost && c.ichra){
                if(config.individual_coverage) {
                    if (!config.fullCoverage) {
                        const coverage = await new CoreCall('coverages', context).get(config.individual_coverage)
                            .catch(err => console.log(`Error getting individual coverage in enrollment contribution totaling: ${err.message}`))
                        if (coverage.type === 'hs') isPost = true;
                    } else if (config.fullCoverage.type === 'hs') isPost = true;
                }
            }
            if (isPost) {
                postTax += num;
                postByCoverageId[cov._id] = true
            }
            /** Intentionally if an employer contributes toward a specific coverage, that coverage cannot be a post-tax option. This is because in many scenarios post-tax options need to be only available via the cafeteria plan - and it's not worth mixing in the scenarios where that isn't true. If you want to make a post-tax contribution, just use the cafeteria plan */
            if (Object.keys(ec).length) {
                const et = isPost ? 0 : Math.min(isNaN(ecTotal) ? 0 : ecTotal || 0, num)
                employerCoverages += et
                num -= et
                byCoverage[cov._id].employer = et;
            }
            byCoverage[cov._id].employee = num;
            // if (cov.postTax || cov.covered === 'individual') postTax += num;

        }

        const employeeCafe = getElectedContributions(enrollment)

        /** Needs that must be met by cafeteria plan contributions (employee + employer) we don't know how much employee needs to contribute until calculations are adjusted below*/
        const coverageCafe = { pop: 0, cash: 0 };
        for(const covId in byCoverage){
            const { employee } = byCoverage[covId];
            if(postByCoverageId[covId]) coverageCafe.cash += employee;
            else {
                coverageCafe.pop += employee;
           }
        }

        const employerCafe = getEmployerFlexCredits(plan, {
            enrollment,
            needs: { preTax: coverageCafe.pop, postTax: coverageCafe.cash, total: coverageCafe.pop + coverageCafe.cash  },
            totalIncome: ttl,
            baseIncome: base,
            contributions: employee.total
        })

        const patchObj:any = {}
        if(coverageCafe.pop !== employeeCafe.byPlan.pop){
            patchObj[`cafe.pop.amount`] = coverageCafe.pop;
            patchObj[`cafe.pop.optOut`] = false;
            const diff = (employeeCafe.byPlan.pop - coverageCafe.pop)
            employeeCafe.total -= diff;
            employeeCafe.preTax -= diff;
            employeeCafe.byPlan.pop = coverageCafe.pop;
        }

        if(coverageCafe.cash > employeeCafe.byPlan.cash){
            patchObj[`cafe.cash.amount`] = coverageCafe.cash;
            patchObj[`cafe.cash.optOut`] = false;
            const diff = coverageCafe.cash - employeeCafe.byPlan.cash;
            employeeCafe.total += diff;
            employeeCafe.postTax += diff;
            employeeCafe.byPlan.cash = coverageCafe.cash;
        }

        /** Leave unused cafeteria contributions to the cash */
        if(employeeCafe.total < employerCafe){
            if(patchObj[`cafe.cash.amount`]) patchObj[`cafe.cash.amount`] += (employerCafe - employeeCafe.total);
            else patchObj[`cafe.cash.amount`] = employeeCafe.byPlan.cash + (employerCafe - employeeCafe.total);
        }
        if(Object.keys(patchObj).length){
            await new CoreCall('enrollments', context)._patch(enrollment._id, {$set: patchObj}, {
                skip_hooks: true,
                admin_pass: true
            })
                .catch(err => console.log(`Error patching enrollment with calculated cafeteria contributions. Enrollment id ${enrollment._id}. Err: ${err.message}`))
        }

        const needed = {
            total: needTotal,
            postTax,
            preTax: needTotal - postTax
        }
        const employerCafe = getEmployerFlexCredits(plan, {
            enrollment,
            needs: needed,
            totalIncome: ttl,
            baseIncome: base,
            contributions: employeeCafe.total
        })
        const employer = {
            cafe: employerCafe,
            coverages: employerCoverages
        }
        return {employee: employeeCafe, employer, needed, byCoverage, byPlan}
    }
}


