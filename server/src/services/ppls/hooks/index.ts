import {hooks as schemaHooks} from '@feathersjs/schema';
import {
    pplsDataResolver,
    pplsDataValidator,
    pplsExternalResolver, pplsPatchResolver, pplsPatchValidator,
    pplsQueryResolver,
    pplsQueryValidator,
    pplsResolver
} from '../ppls.schema.js';

import {
    logChange,
    findJoin,
    scrubUploads,
    getJoin,
    encryptedFields,
    singleEmail,
    multiEmail, _get, _flatten
} from '../../../utils/index.js';

import { makeDataArraySafe } from '../../../utils/array-safe-hooks.js';

import {
    allUcanAuth,
    CapabilityParts,
    anyAuth,
    CoreCall,
    loadExists,
    setExists,
    noThrowAuth, noThrow,
} from 'feathers-ucan';

import {HookContext} from '../../../declarations.js';
import {ObjectId} from 'mongodb';


const authenticate = async (context: HookContext) => {
    // Allow explicit special changes for special_change
    const creator = [['ppls', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['ppls', '*']] as Array<CapabilityParts>;
    const ucanArgs:any = {
        find: noThrow,
        create: noThrow,
        patch: [...creator],
        update: [...creator],
        remove: deleter
    }
    if(context.method === 'patch'){
        const ex = await loadExists(context);
        context = setExists(context, ex);
        if(!ex?.login && context.params.special_change) {
            ucanArgs.patch = noThrow
            context.params.admin_pass = true;
            // Handle array-safe cleanupFlag setting
            if (Array.isArray(context.data)) {
                context.data = context.data.map(item => ({ ...item, cleanupFlag: true }));
            } else {
                context.data.cleanupFlag = true;
            }
        } else if(context.params.special_change) {
            if(context.params.special_change === anyAuth) context.params.specialChange = ['inOrgs', 'inGroups', 'address', 'addresses', 'emails', 'phones', 'avatar', 'ims', 'invites']
        }
    }
    return await allUcanAuth<HookContext>(ucanArgs, {
        loginPass: [[['_id/owner'], '*']],
        adminPass: ['patch', 'create', 'remove'],
        or: '*',
        specialChange: context.params.specialChange
    })(context) as any;
}


//TODO: write aggregate pipeline for single call
const syncGroups = (time = 60) => {
    return async (context: HookContext) => {
        if (!context.params.core._syncing_groups && !context.params.skip_hooks) {
            let run = false;
            const {lastGroupSync} = context.result;
            if (!lastGroupSync) run = true
            else {
                const mDate = (new Date(lastGroupSync).getTime() || 0) / 1000 / 60;
                const curr = new Date().getTime() / 1000 / 60;
                if ((curr - mDate) > time) run = true
            }
            if (run) {
                context.params.core._syncing_groups = true;
                const groups = await new CoreCall('groups', context, {skipJoins: true})._find({
                    skip_hooks: true, admin_pass: true,
                    query: {
                        deleted: {$ne: true},
                        $limit: 25,
                        members: { $in: [context.result._id] }
                    }
                })
                if (groups.total) {
                    const ids: Array<string> = [];
                    const orgIds: Array<string> = [];
                    for (const grp of groups.data || []) {
                        ids.push(grp._id);
                        orgIds.push(grp.org)
                    }
                    const patchObj: any = {
                        lastGroupSync: new Date()
                    }
                    if (ids.length) patchObj.$addToSet = {inGroups: {$each: ids}};
                    if (orgIds.length) patchObj.$addToSet = {...patchObj.$addToSet, inOrgs: {$each: orgIds}}
                    const synced = await new CoreCall('ppls', context)._patch(context.id as any, patchObj, { skip_hooks: true, admin_pass: true})
                    context.result = synced
                }
            }
        }
        return context;
    }
}
const getOwns = () => {
    return async context => {
        if (context.params.runJoin?.owns) {

            return findJoin({
                service: 'orgs',
                therePath: 'owners.id',
                joinPath: ['owns'],
                herePath: '_id'
            })(context);
        } else return context;
    };
};

const getLogin = () => {
    return async (context: HookContext): Promise<HookContext> => {
        if (context.params.runJoin?.login) {
            return getJoin({
                name: 'joinLogin',
                service: 'logins',
                herePath: 'login',
                params: {core: {...context.params.core, skipJoins: true}}
            })(context);
        } else return context;
    }
};


const imagePaths = ['avatar'];


const formatSingle = (data: any): any => {
    const obj = {
        'name': (val) => (val || '').trim(),
        'ssn': (val) => (val || '').split('-').join(''),
        'email': (val) => singleEmail(val),
        'emails': (val) => multiEmail(val)
    }
    Object.keys(obj).forEach(key => {
        if (data[key]) data[key] = obj[key](data[key]);
    })
    return data;
}

const format = makeDataArraySafe((context: HookContext, item: any) => {
    return formatSingle(item);
});

const addToGroup = async (context: HookContext): Promise<HookContext> => {
    if (context.params.runJoin?.groupId) {
        if(context.type === 'after') {
            delete context.params.addMembers;
            await new CoreCall('groups', context, {skipJoins: true}).patch(context.params.runJoin.groupId, {$addToSet: {members: Array.isArray(context.result) ? { $each: context.result.map(a => a._id) } : context.result._id}})
        } else if(context.type === 'before') {
            const groupId = context.params.runJoin.groupId;
            if(Array.isArray(context.data)) context.data = context.data.map(a => {
                a.$addToSet = { ...a.$addToSet, inGroups: groupId }
                return a;
            })
            else context.data.$addToSet = { ...context.data.$addToSet, inGroups: groupId }
        }
    }
    return context;
}

const nameSyncSingle = (data: any): any => {
    let name = data.name || data.$set?.name;
    let firstName = data.firstName || data.$set?.firstName;
    let lastName = data.lastName || data.$set?.lastName;
    if (name) {
        if (lastName && firstName) data.name = firstName + ' ' + lastName;
        else {
            const ns = name?.split(' ') || [];
            data.lastName = lastName || ns[ns.length - 1];
            data.firstName = firstName || ns[0];
        }
    } else if(firstName && lastName){
        data.name = firstName + ' ' + lastName;
    }
    return data;
}

const nameSync = makeDataArraySafe((context: HookContext, item: any) => {
    return nameSyncSingle(item);
});

// const allowSpecialChange = async (context: HookContext): Promise<HookContext> => {
//     if (context.params.special_change) {
//         const exists = await loadExists(context);
//         context = setExists(context, exists);
//         if (exists?.login) {
//             const whitelist = ['emails', 'phones', 'addresses', 'stripeAccounts'];
//             const maybe = ['firstName', 'lastName', 'dob', 'ssn', 'last4Ssn'];
//             for (const k in context.data) {
//                 if (!whitelist.includes(k)) {
//                     if (_get(exists, k) || !maybe.includes(k.split('.')[0])) {
//                         delete context.data[k];
//                     }
//                 }
//             }
//             for (const k in context.data.$set || {}) {
//                 if (!whitelist.includes(k)) {
//                     if (_get(exists, k) || !maybe.includes(k.split('.')[0])) {
//                         delete context.data.$set[k];
//                     }
//                 }
//             }
//         }
//     }
//     return context;
// }

const limitSearch = async (context: HookContext): Promise<HookContext> => {
    if(context.params.skip_hooks) return context;

    //if all permissions exist
    // const ctx = await ucanAuth([['ppls', 'READ']], {noThrow: true})(context) as any;
    if (!context.params.admin_pass) {
        // if (ctx.params._no_throw_error && !context.params.admin_pass) {
        //otherwise
        const {owner} = context.params.login || [];
        if (owner) {
            const {_id, inOrgs, household, inGroups} = context.params.query;
            if (!_id && !inOrgs && !inGroups && !household && !context.params._search?.people_search) {
                const person = await new CoreCall('ppls', context, {skipJoins: true}).get(owner)
                //TODO: write a pipeline query for this
                //TODO:consider tightening this to just groups - orgs is useful but maybe a bit too permissive leaving the code for groups below
                // const groups = await new CoreCall('groups', ctx, {skipJoins: true}).find({query: {_id: {$in: person?.inGroups || []}}})

                const orgs = await new CoreCall('orgs', context, {skipJoins: true}).find({query: {_id: {$in: (person?.inOrgs || []).filter(a => !!a)}}})
                const $or = [...context.params.query.$or || []]
                $or.push({inOrgs: {$in: (orgs.data || []).map(a => a._id)}})
                $or.push({_id: {$in: _flatten(orgs.data.map(a => (a.owners || []).filter(o => o.idService === 'ppls').map(o => o.id)))}})
                if (person.household) $or.push({household: person.household})
            }
        } else if((!context.params._search && !context.params.runJoin?.hh_members) || !context.params.query._id) throw new Error('Search people error - cannot search people when not authenticated')
    }
    return context;
}

const handleNamesSingle = (data: any): any => {
    if (data.name && !data.firstName) {
        const spl = data.name.split(' ');
        data.firstName = spl[0];
        data.lastName = spl.slice(1).join(' ');
    }
    return data;
}

const handleNames = makeDataArraySafe((context: HookContext, item: any) => {
    return handleNamesSingle(item);
});


///COMMONCARE SPECIFIC FUNCTIONS
/** you can set data.comp - as a comp object ot use for creating a cams package */
const handleCompBefore = async (context: HookContext): Promise<HookContext> => {
    if(Array.isArray(context.data)) return context;
    const {comp, hireDate} = context.data;
    if (comp) {
        context.params._init_cam = true;
        const pickList = ['org', 'contract', 'interval', 'estHours', 'amount', 'terms', 'name', 'class', 'extras'];
        const personId = context.id || new ObjectId()
        if (!context.id) context.data._id = personId;
        const cam: any = {person: personId, hireDate: hireDate || new Date()}
        for (const k of pickList) {
            cam[k] = comp[k] || undefined
        }
        cam.comp = comp._id
        delete context.data.comp;
        if (hireDate) delete context.data.hireDate;
        const cams = await new CoreCall('cams', context, {skipJoins: true}).create(cam, {
            admin_pass: true,
            _init_cam: true
        })
            .catch(err => {
                console.log(`Error creating new cams for new person: ${personId} - ${err.message}`)
            })
        if (context.method === 'create') context.data.cams = [cams._id];
        else context.data.$addToSet ? context.data.$addToSet.cams = cams._id : context.data.$addToSet = {cams: cams._id}
    }

    return context;
}

const removeProvisionalCam = async (context: HookContext): Promise<HookContext> => {
    if (context.params._init_cam && !context.result?._id) {
        // Handle array-safe access to cams
        if (Array.isArray(context.data)) {
            // For arrays, we need to handle each item that might have cams
            for (const item of context.data) {
                if (item.cams && item.cams[0]) {
                    await new CoreCall('cams', context).remove(item.cams[0], {admin_pass: true})
                        .catch(err => console.log(`Error removing provisional cam: ${err.message}`));
                }
            }
        } else if (context.data.cams && context.data.cams[0]) {
            await new CoreCall('cams', context).remove(context.data.cams[0], {admin_pass: true})
        }
    }
    return context;
}

const runJoins = async (context: HookContext): Promise<HookContext> => {
    const {runJoin} = context.params;
    if(context.result && !runJoin?.ai_chats) delete context.result.ai_chats;
    if (runJoin) {
        if (runJoin.orgGroup) {
            return findJoin({
                service: 'groups',
                herePath: 'inGroups',
                therePath: '_id',
                joinPath: 'orgGroups',
                queryFn: (item: any) => {
                    return {
                        $select: ['name', '_id'],
                        _id: {$in: item.inGroups || []},
                        org: runJoin.orgGroup
                    }
                }
            })(context)
        }
    }
    return context;
}

const cleanupInsert = async (context: HookContext) => {
    if(context.result._id && !context.result.createdBy?.login){
        await new CoreCall('ppls', context, { skipJoins: true }).patch(context.result._id, { cleanupFlag: true }, { admin_pass: true })
            .catch(err => console.error(`Could not insert cleanup flag on ppls ${err.message}`))
    }
    return context;
}

const addHousehold = async (context: HookContext): Promise<HookContext> => {
    if(Array.isArray(context.data)) return context;
    const { add_household } = context.params.runJoin || {}
    if(add_household){
        const hh = await new CoreCall('households', context).create({ person: context.result._id })
        const patchObj = {members: {}}
        if(add_household.spouse){
            const spouse = await new CoreCall('ppls', context).create({...add_household.spouse, household: hh._id })
                .catch(err => console.log(`Error adding spouse to household: ${err.message}`));
            if(spouse) patchObj.members[spouse._id] = { relation: 'spouse' }
        }
        if(add_household.deps){
            const promises:any = [];
            for(let i = 0; i < add_household.deps.length; i++){
                promises.push(new CoreCall('ppls', context).create({ ...add_household.deps[i], household: hh._id })
                    .catch(err => console.log(`Error adding dep ${i} to household ${hh._id}: ${err.message}`)))
            }
            await Promise.all(promises)
            for(const d of promises){
                if(d?._id) patchObj.members[d._id] = { relation: 'child', dependent: true }
            }
        }
        if(Object.keys(patchObj.members).length) await new CoreCall('households', context, { skipJoins: true }).patch(hh._id, patchObj)
            .catch(err => console.log(`Error adding household members: ${err.message}`))
    }
    return context;
}

/** AI NOTES */
/** The purpose of the bypass validator and inject bypassed is to move whitelisted data paths to params to bypass the json schema validators and then move the data back after validation. This allows you to make nested path patches without loosening the data validation for all cases */
import { bypassValidators, injectBypassed } from '../../../utils/schemas/bypass.js';
const validatorPaths = {
    $addToSet: [/^\$addToSet\.invites\.[^.]+\.[Rr]eminded$/]
}

const contactPaths = [['email', 'emails'], ['phone', 'phones'], ['address', 'addresses']];
import {addContacts} from '../../orgs/hooks/index.js';

export const pplHooks = {
    around: {
        all: [
            schemaHooks.resolveExternal(pplsExternalResolver),
            schemaHooks.resolveResult(pplsResolver)
        ]
    },
    before: {
        all: [
            authenticate,
            logChange(),
            schemaHooks.validateQuery(pplsQueryValidator),
            schemaHooks.resolveQuery(pplsQueryResolver),
            scrubUploads({paths: imagePaths}),
            encryptedFields(['dob', 'ssn'])
        ],
        find: [
            // groupQuery,
            limitSearch
        ],
        get: [
            noThrowAuth
        ],
        create: [
            handleCompBefore,
            handleNames,
            addContacts(contactPaths),
            schemaHooks.validateData(pplsDataValidator),
            schemaHooks.resolveData(pplsDataResolver),
            nameSync,
            format,
        ],
        update: [
            schemaHooks.validateData(pplsPatchValidator),
            schemaHooks.resolveData(pplsPatchResolver)
        ],
        patch: [
            bypassValidators(validatorPaths),
            handleCompBefore,
            addContacts(contactPaths),
            nameSync,
            format,
            schemaHooks.validateData(pplsPatchValidator),
            schemaHooks.resolveData(pplsPatchResolver),
            injectBypassed
        ],
        remove: []
    },

    after: {
        all: [
            scrubUploads({paths: imagePaths}),
            encryptedFields(['dob', 'ssn']),
            runJoins
        ],
        find: [
            getLogin()
        ],
        get: [
            getOwns(),
            getLogin(),
            syncGroups(60)
        ],
        create: [addToGroup, addHousehold, cleanupInsert],
        update: [],
        patch: [
            addToGroup,
            syncGroups(5)
        ],
        remove: []
    },

    error: {
        all: [],
        find: [],
        get: [],
        create: [
            removeProvisionalCam
        ],
        update: [],
        patch: [],
        remove: []
    }
};
