// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'
import type { ClientApplication } from '../../client.js'
import type {
  TomtomReverseGeocode,
  TomtomReverseGeocodeData,
  TomtomReverseGeocodePatch,
  TomtomReverseGeocodeQuery,
  TomtomReverseGeocodeService
} from './tomtom-reverse-geocode.class.js'

export type {
  TomtomReverseGeocode,
  TomtomReverseGeocodeData,
  TomtomReverseGeocodePatch,
  TomtomReverseGeocodeQuery
}

export type TomtomReverseGeocodeClientService = Pick<
  TomtomReverseGeocodeService<Params<TomtomReverseGeocodeQuery>>,
  (typeof tomtomReverseGeocodeMethods)[number]
>

export const tomtomReverseGeocodePath = 'tomtom-reverse-geocode'

export const tomtomReverseGeocodeMethods = ['find', 'get', 'create', 'patch', 'remove'] as const

export const tomtomReverseGeocodeClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(tomtomReverseGeocodePath, connection.service(tomtomReverseGeocodePath), {
    methods: tomtomReverseGeocodeMethods
  })
}

// Add this service to the client service type index
declare module '../../client.js' {
  interface ServiceTypes {
    [tomtomReverseGeocodePath]: TomtomReverseGeocodeClientService
  }
}
