import {HookContext} from '../../../declarations';
import {CoreCall} from 'feathers-ucan';
import { makeResultArraySafe } from '../../../utils/array-safe-hooks.js';

export * from './sync-amounts.js';
export * from './mcc.js';

const looseRelateUsersSingle = (path: 'card_user' | 'budget_user' | 'care_account_user') => {
    return async (context: HookContext, result: any): Promise<any> => {
        const {$set, $pull, $addToSet, ...rest} = context.data || {};
        const superObj = {...$set, ...$pull, ...$addToSet, ...rest};
        const arr = [];
        for (const k of ['members', 'managers', 'approvers']) {
            if (superObj[k]) arr.concat(result[k] || [])
        }
        if (arr.length) await new CoreCall('ppls', context, {skipJoins: true})._patch(null, {[context.method === 'remove' ? '$pull' : '$addToSet']: {[path]: result._id}}, {skip_hooks: true, admin_pass: true, query: {_id: {$in: arr}}})
        return result;
    }
}

export const looseRelateUsers = (path: 'card_user' | 'budget_user' | 'care_account_user') => {
    return makeResultArraySafe(looseRelateUsersSingle(path));
}
