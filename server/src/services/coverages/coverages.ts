// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    coveragesDataValidator,
    coveragesPatchValidator,
    coveragesQueryValidator,
    coveragesResolver,
    coveragesExternalResolver,
    coveragesDataResolver,
    coveragesPatchResolver,
    coveragesQueryResolver
} from './coverages.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {CoveragesService, getOptions} from './coverages.class.js'
import {coveragesPath, coveragesMethods} from './coverages.shared.js'

import {allUcanAuth, CapabilityParts, CoreCall, loadExists, setExists} from 'feathers-ucan';
import {logChange, scrubUploads} from '../../utils/index.js';
import { makeDataArraySafe, makeResultArraySafe } from '../../utils/array-safe-hooks.js';

export * from './coverages.class.js'
export * from './coverages.schema.js'

const authenticate = async (context: HookContext) => {
    const writer = [['coverages', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['coverages', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        create: [...writer],
        patch: [...writer],
        update: [...writer],
        remove: [...deleter]
    };
    const cap_subjects: any = []


    if (!['get', 'find'].includes(context.method)) {
        let existing = {by: undefined, issuer: undefined, org: undefined};
        if (context.method !== 'create') {
            existing = await loadExists(context);
            context = setExists(context, existing);
        }
        let orgWrite: any[] = [];

        // Handle array-safe property access
        const getDataProperty = (prop: string) => {
            if (Array.isArray(context.data)) {
                // For arrays, get the property from the first item (common pattern for bulk operations)
                return existing[prop] || context.data[0]?.[prop];
            } else {
                return existing[prop] || context.data?.[prop];
            }
        };

        const byId = getDataProperty('by');
        if (byId) {
            const byNamespcace = `orgs:${byId}`;
            cap_subjects.push(byId)
            orgWrite.push([byNamespcace, 'WRITE'], [byNamespcace, 'orgAdmin'])
        }
        const issuerId = getDataProperty('issuer');
        if (issuerId) {
            const issuerNamespace = `orgs:${issuerId}`;
            cap_subjects.push(issuerId)
            orgWrite.push([issuerNamespace, 'WRITE'])
        }
        const orgId = getDataProperty('org');
        if (orgId) {
            const orgNamespace = `orgs:${orgId}`;
            cap_subjects.push(orgId)
            orgWrite.push([orgNamespace, 'WRITE'], [orgNamespace, 'orgAdmin'])
        }

        for (const w of orgWrite) {
            ucanArgs.create.unshift(w);
            ucanArgs.patch.unshift(w);
            ucanArgs.update.unshift(w);
            ucanArgs.remove.unshift(w);
        }
    }
    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['patch', 'create', 'remove'],
        cap_subjects,
        or: '*'
    })(context) as any;
}

import {getCoverageRate} from '../enrollments/utils/index.js';

const getFortyPremiumSingle = async (context: HookContext, coverage: any): Promise<any> => {
    const args = {
        coverage,
        def_age: 40,
        enrolled: [{age: 40, relation: 'self'}]
    }
    const fortyPremium = getCoverageRate(args)
    if (fortyPremium !== coverage.fortyPremium) {
        coverage.fortyPremium = fortyPremium
        new CoreCall('coverages', context).patch(coverage._id, {fortyPremium})
            .catch(err => console.log(`Error patching forty premium on record ${coverage._id}: ${err.message}`))
    }
    return coverage;
}

const getFortyPremium = makeResultArraySafe(getFortyPremiumSingle);


import multer from 'multer';
import {fillFixedRates, rateUpload} from './utils/rates.js';

const multipartMiddleware = multer();
const restMiddleware = async (req, res, next) => {
    await new Promise((resolve) => multipartMiddleware.single('file')(req, res, resolve));
    req.feathers.file = req.file;
    req.feathers.addCoverage = req.query.addCoverage
    req.feathers.core = req.query.core
    return next();
}

const paths = ['documents', 'carrierLogo'];
const uploadsConfig = {paths};

const checkCarrierLogoSingle = async (context: HookContext, result: any): Promise<any> => {
    if (result.carrierName) {
        // if(context.data.carrierLogo && result.carrierName){
        const itemId = `carrier_logos:${result.carrierName.split(' ').join('_').toLowerCase()}`
        const ex = context.params[itemId] || await new CoreCall('junk-drawers', context).find({
            query: {
                itemId: `carrier_logos:${result.carrierName.split(' ').join('_').toLowerCase()}`,
                $limit: 1
            }
        })
            .catch(err => console.log(`Error searching for carrier drawer to add carrier logo: ${err.message}`))
        if (ex) {
            context.params.itemId = ex;
            if (ex.total) {
                if (!(ex.data[0]?.data || {})[result.carrierLogo.uploadId]) {
                    new CoreCall('junk-drawers', context).patch(ex.data[0]._id, {[`data.${result.carrierLogo.uploadId}`]: result.carrierLogo})
                        .catch(err => console.log(`Error patching carrier logo junk drawer update from coverage: ${err.message}`))
                }
            } else {
                new CoreCall('junk-drawers', context).create({
                    drawer: 'carrier_logos',
                    itemName: result.carrierName.split(' ').join('_').toLowerCase(),
                    data: {[String(result.carrierLogo.uploadId)]: result.carrierLogo}
                })
                    .catch(err => console.log(`Error creating carrier logo junk drawer update from coverage: ${err.message}`))
            }
        }
    }
    return result;
}

const checkCarrierLogo = makeResultArraySafe(checkCarrierLogoSingle);

import {ai_chat_get} from './ai/index.js';

const syncIchraWithShopSingle = async (context: HookContext, result: any): Promise<any> => {
    if (result.ichra && !result.shop) {
        await new CoreCall('coverages', context).patch(result._id, {shop: true})
            .catch(err => console.log(`Error syncing ichra with shop for ${result._id}: ${err.message}`));
        result.shop = true; // Update the result object to reflect the change
    }
    return result;
}

const syncIchraWithShop = makeResultArraySafe(syncIchraWithShopSingle);
const ifIchraThenShopSingle = (data: any): any => {
    if (data.ichra) data.shop = true;
    return data;
}

const ifIchraThenShop = makeDataArraySafe((context: HookContext, item: any) => {
    return ifIchraThenShopSingle(item);
});

// A configure function that registers the service and its hooks via `app.configure`
export const coverages = (app: Application) => {
    // Register our service on the Feathers application
    // @ts-ignore
    app.use(coveragesPath,
        restMiddleware,
        new CoveragesService(getOptions(app)), {
            // A list of all methods this service exposes externally
            'methods': coveragesMethods,
            // You can add additional custom events to be sent to clients here
            'events': []
        })
    // Initialize hooks
    app.service(coveragesPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(coveragesExternalResolver),
                schemaHooks.resolveResult(coveragesResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(coveragesQueryValidator),
                schemaHooks.resolveQuery(coveragesQueryResolver),
                scrubUploads(uploadsConfig)
            ],
            find: [],
            get: [],
            create: [
                ifIchraThenShop,
                schemaHooks.validateData(coveragesDataValidator),
                schemaHooks.resolveData(coveragesDataResolver),
                fillFixedRates
            ],
            patch: [
                schemaHooks.validateData(coveragesPatchValidator),
                schemaHooks.resolveData(coveragesPatchResolver),
                rateUpload,
                fillFixedRates
            ],
            remove: []
        },
        after: {
            all: [scrubUploads(uploadsConfig)],
            create: [getFortyPremium, checkCarrierLogo],
            patch: [rateUpload, getFortyPremium, checkCarrierLogo, syncIchraWithShop],
            get: [ai_chat_get],
            find: []
        },
        error: {
            all: [
                // ctx => {
                //     console.log('err', ctx.error)
                // }
            ]
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [coveragesPath]: CoveragesService
    }
}
