import { HookContext } from '../declarations.js';

/**
 * Utility functions for making Feathers.js hooks array-safe
 * These functions help hooks handle both single objects and arrays in context.data or context.result
 */

/**
 * Creates an array-safe version of a hook function that operates on context.data
 * @param hookFn - The original hook function that operates on a single item
 * @returns A new hook function that handles both arrays and single objects
 */
export function makeDataArraySafe<T = any>(
    hookFn: (context: HookContext, item: T, index?: number) => T | Promise<T>
) {
    return async (context: HookContext): Promise<HookContext> => {
        if (!context.data) return context;

        if (Array.isArray(context.data)) {
            // Handle array of items
            const processedItems = await Promise.all(
                context.data.map(async (item, index) => {
                    const tempContext = { ...context, data: item };
                    const result = await hookFn(tempContext, item, index);
                    return result;
                })
            );
            context.data = processedItems;
        } else {
            // Handle single item
            const result = await hookFn(context, context.data);
            context.data = result;
        }

        return context;
    };
}

/**
 * Creates an array-safe version of a hook function that operates on context.result
 * @param hookFn - The original hook function that operates on a single item
 * @returns A new hook function that handles both arrays and single objects
 */
export function makeResultArraySafe<T = any>(
    hookFn: (context: HookContext, item: T, index?: number) => T | Promise<T>
) {
    return async (context: HookContext): Promise<HookContext> => {
        if (!context.result) return context;

        if (Array.isArray(context.result)) {
            // Handle array of items
            const processedItems = await Promise.all(
                context.result.map(async (item, index) => {
                    const tempContext = { ...context, result: item };
                    const result = await hookFn(tempContext, item, index);
                    return result;
                })
            );
            context.result = processedItems;
        } else {
            // Handle single item
            const result = await hookFn(context, context.result);
            context.result = result;
        }

        return context;
    };
}

/**
 * Creates an array-safe version of a hook function that may operate on either context.data or context.result
 * @param hookFn - The original hook function
 * @param options - Configuration options
 * @returns A new hook function that handles both arrays and single objects
 */
export function makeHookArraySafe(
    hookFn: (context: HookContext) => HookContext | Promise<HookContext>,
    options: {
        dataProperty?: 'data' | 'result';
        skipIfArray?: boolean; // If true, skip processing when data is an array
    } = {}
) {
    return async (context: HookContext): Promise<HookContext> => {
        const { dataProperty, skipIfArray = false } = options;
        
        // If skipIfArray is true and the relevant property is an array, skip processing
        if (skipIfArray) {
            const targetData = dataProperty ? context[dataProperty] : (context.data || context.result);
            if (Array.isArray(targetData)) {
                return context;
            }
        }

        return await hookFn(context);
    };
}

/**
 * Utility function to safely access properties on data that might be an array
 * @param data - The data (could be single object or array)
 * @param accessor - Function to access/modify the data
 * @returns Modified data maintaining original structure
 */
export function safeDataAccess<T, R>(
    data: T | T[],
    accessor: (item: T, index?: number) => R
): R | R[] {
    if (Array.isArray(data)) {
        return data.map((item, index) => accessor(item, index));
    } else {
        return accessor(data);
    }
}

/**
 * Utility function to safely modify properties on context.data or context.result
 * @param context - The hook context
 * @param property - The property to modify ('data' or 'result')
 * @param modifier - Function to modify each item
 * @returns Modified context
 */
export function safeContextModify<T>(
    context: HookContext,
    property: 'data' | 'result',
    modifier: (item: T, index?: number) => T
): HookContext {
    const targetData = context[property];
    if (!targetData) return context;

    if (Array.isArray(targetData)) {
        context[property] = targetData.map((item, index) => modifier(item, index));
    } else {
        context[property] = modifier(targetData);
    }

    return context;
}

/**
 * Creates a wrapper that ensures a hook only processes single objects, not arrays
 * Useful for hooks that should only run on individual items
 * @param hookFn - The hook function that should only process single objects
 * @returns A wrapped hook function that skips array processing
 */
export function singleObjectOnly(
    hookFn: (context: HookContext) => HookContext | Promise<HookContext>
) {
    return async (context: HookContext): Promise<HookContext> => {
        // Skip if context.data is an array
        if (Array.isArray(context.data)) {
            return context;
        }
        
        // Skip if context.result is an array
        if (Array.isArray(context.result)) {
            return context;
        }

        return await hookFn(context);
    };
}

/**
 * Helper function to check if context contains array data
 * @param context - The hook context
 * @param property - The property to check ('data' or 'result' or 'both')
 * @returns True if the specified property contains an array
 */
export function hasArrayData(context: HookContext, property: 'data' | 'result' | 'both' = 'both'): boolean {
    if (property === 'data') {
        return Array.isArray(context.data);
    } else if (property === 'result') {
        return Array.isArray(context.result);
    } else {
        return Array.isArray(context.data) || Array.isArray(context.result);
    }
}
